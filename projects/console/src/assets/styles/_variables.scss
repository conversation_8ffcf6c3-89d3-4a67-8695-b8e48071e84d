:root {
  // Font family
  --font-family: 'Mulish', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  --font-size: 16px;
  --line-height: 1.5;
  
  // Light Theme Variables (default)
  
  // Core colors
  --background-color: #ffffff;
  --text-color: #333333;
  --text-secondary: #666666;
  
  // Background image
  // --bg-image: url('assets/images/bg-light.png') !important;
  
  // Glass effect
  --glass-bg: linear-gradient(102.14deg, rgba(255, 255, 255, 0.5) 1.07%, rgba(255, 255, 255, 0.6) 98.01%);
  --glass-border: rgba(255, 255, 255, 0.3);
  --glass-shadow: rgba(31, 38, 135, 0.07);
  
  // Branding colors
  --primary-start: #6566CD;
  --primary-end: #F96CAB;
  --gradient-primary: linear-gradient(90deg, var(--primary-start) 0%, var(--primary-end) 100%);
  
  // Component colors
  --card-bg: white;
  --card-border: rgba(255, 255, 255, 0.2);
  --card-shadow: rgba(0, 0, 0, 0.05);
  --card-hover-shadow: rgba(0, 0, 0, 0.08);
  --card-hover-border: rgba(102, 109, 153, 0.3);
  --card-border-radius: 12px;
  --card-border-color: rgba(255, 255, 255, 0.2);
  --card-box-shadow: 0 4px 16px rgba(0, 0, 0, 0.05);
  --card-header-bg: transparent;
  --card-header-color: var(--text-color);
  --card-body-bg: transparent;
  --card-body-color: var(--text-color);
  --card-footer-bg: transparent;
  --card-footer-color: var(--text-color);
  
  // Dashboard specific colors
  --dashboard-primary: #6566CD;
  --dashboard-secondary: #F96CAB;
  --dashboard-text-primary: #333;
  --dashboard-text-secondary: #666;
  --dashboard-text-kpi-title: #666D99;
  --dashboard-text-kpi-value: #3D415C;
  --dashboard-border-light: #F0F0F5;
  --dashboard-bg-light: #f8f8f8;
  --dashboard-bg-lighter: #f5f5f5;
  --dashboard-bg-action-button: #fff;
  --dashboard-bg-icon-button: #F6F6FE;
  --dashboard-quick-action-button-bg: var(--gradient-primary);
  --dashboard-quick-action-text: #ffffff;
  --dashboard-text-welcome: #33364D;
  --dashboard-shadow-color: rgba(0, 0, 0, 0.05);
  --dashboard-shadow-hover: rgba(101, 102, 205, 0.15);
  --dashboard-shadow-medium: rgba(0, 0, 0, 0.08);
  --dashboard-shadow-strong: rgba(0, 0, 0, 0.12);
  --dashboard-border-accent: rgba(101, 102, 205, 0.2);
  --dashboard-action-button-border: #e0e0e0;
  --dashboard-card-bg: #FFFFFF;
  --dashboard-scrollbar-track: #f6f6f6;
  --dashboard-scrollbar-thumb: rgba(101, 102, 205, 0.15);
  --dashboard-scrollbar-thumb-hover: rgba(101, 102, 205, 0.3);
  --dashboard-scrollbar-thumb-border: rgba(101, 102, 205, 0.05);
  --dashboard-toggle-stroke: #6566CD;
  --dashboard-toggle-stroke-collapsed: white;
  --dashboard-fading-border: rgba(240, 240, 245, 0.6);
  --dashboard-table-row-border: rgba(240, 240, 245, 0.6);
  --dashboard-copyright: #6E6E80;
  --dashboard-approve-button-bg: #D6FFF6;
  --dashboard-approve-button-color: #474C6B;
  --dashboard-approve-button-border: #02D4A6;
  --dashboard-approve-button-shadow: rgba(2, 212, 166, 0.15);
  
  // DataCard specific
  --tag-bg: rgba(255, 255, 255, 0.5);
  --tag-text: #333333;
  --tag-border: rgba(120, 117, 117, 0.2);
  --subtag-bg: rgba(248, 248, 248, 0.5);
  --subtag-text: #666666;
  --subtag-border: rgba(238, 238, 238, 0.5);
  --action-icon-color: #858AAD;
  --action-button-hover: rgba(240, 240, 240, 0.7);
  --action-button-hover-color: #333333;
  --view-icon-color: #4a90e2;
  --edit-icon-color: #6cc04a;
  --delete-icon-color: #e25a4a;
  
  // Code styling
  --code-bg: #1e293b;
  --code-color: #f8fafc;
  --code-scrollbar-track: rgba(255, 255, 255, 0.05);
  --code-scrollbar-thumb: rgba(255, 255, 255, 0.2);
  --code-scrollbar-thumb-hover: rgba(255, 255, 255, 0.3);
  
  // Status indicators
  --status-active-color: #33364D;
  --status-active-border: #01B48D;
  --status-inactive-bg: rgba(231, 76, 60, 0.1);
  --status-inactive-color: #e74c3c;
  --status-pending-bg: rgba(241, 196, 15, 0.1);
  --status-pending-color: #f1c40f;
  
  // Modal
  --modal-bg: rgba(255, 255, 255, 0.95);
  --modal-border: rgba(230, 230, 230, 0.5);
  --modal-shadow: rgba(0, 0, 0, 0.15);
  --modal-header-border: rgba(230, 230, 230, 0.8);
  --modal-footer-border: rgba(230, 230, 230, 0.8);
  --modal-backdrop: rgba(0, 0, 0, 0.5);
  
  // Dropdown components
  --dropdown-bg: #ffffff;
  --dropdown-text: #333333;
  --dropdown-title-color: #000000;
  --dropdown-desc-color: #666666;
  --dropdown-border: #e0e0e0;
  --dropdown-shadow: rgba(0, 0, 0, 0.1);
  --dropdown-hover-bg: #f5f5f5;
  --dropdown-selected-bg: #E6E4FB;
  --dropdown-selected-text: #6566CD;
  --dropdown-placeholder: #999999;
  --dropdown-focus-border: #6566CD;
  --dropdown-focus-shadow: rgba(101, 102, 205, 0.2);
  --dropdown-required-border: #FF4757;
  --dropdown-checkbox-bg: #6566CD;
  --dropdown-checkbox-border: #e0e0e0;
  --dropdown-disabled-bg: #f9f9f9;
  
  // CreateCard specific
  --create-icon-bg: rgba(102, 109, 153, 0.1);
  --create-icon-color: #858AAD;
  --create-icon-hover-bg: rgba(102, 109, 153, 0.15);
  --create-label-color: #666D99;
  
  // Form elements
  --input-bg: #f8f8f9;
  --input-border: #e0e0e0;
  --input-text: #666666;
  --input-placeholder: #999999;
  --input-focus-border: #c0d8ff;
  --input-focus-shadow: rgba(99, 143, 255, 0.1);
  --form-label-color: #666666;
  --form-input-bg: #f8f8f9;
  --form-input-color: #666666;
  --form-input-border: #e0e0e0;
  --form-input-placeholder: #999999;
  --form-input-focus-border: #c0d8ff;
  --form-input-focus-shadow: rgba(99, 143, 255, 0.1);
  
  // Navigation
  --nav-bg: transparent;
  --nav-text: #858AAD;
  --nav-hover: transparent;
  --nav-active: #e6e4fb;
  --nav-active-text: #000000;
  --nav-border: #e0e0e0;
  --nav-item-color: #333333;
  --nav-item-active-color: #000000;
  --nav-item-active-bg: #7fc2eb;

  // Navigation Pills
  --nav-pills-bg: rgba(255, 255, 255, 0.9);
  --nav-pills-shadow: 0 2px 4px #9c9ea0;
  --nav-pill-hover-bg: linear-gradient(118deg, #215ad6 55.27%, #03BDD4);
  --nav-pill-hover-color: #530cf8;
  --nav-pill-hover-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  --nav-pill-hover-icon-filter: brightness(0) invert(1);
  --nav-pill-selected-bg: linear-gradient(118deg, #215ad6 55.27%, #03BDD4 );
  --nav-pill-selected-color: #2b33c4;
  --nav-pill-selected-shadow: 0 2px 4px rgba(0, 0, 0, 0.08);
  --nav-pill-selected-icon-filter: brightness(1) invert(0);
  
  // Buttons
  --button-primary-bg: var(--gradient-primary);
  --button-primary-text: #ffffff;
  --button-secondary-bg: transparent;
  --button-secondary-border: #6566CD;
  --button-secondary-text: #6566CD;
  --button-secondary-hover-bg: rgba(101, 102, 205, 0.05);
  --button-secondary-hover-border: #5455af;
  --button-secondary-hover-text: #5455af;
  --button-secondary-active-bg: rgba(101, 102, 205, 0.1);
  --button-hover-opacity: 0.9;
  
  // Data display
  --table-header-bg: #f8f9fa;
  --table-border: #e0e0e0;
  --table-row-hover: #f5f5fa;
  --table-header-text: #333333;
  --table-text: #666666;
  --table-stripe-bg: #f9f9fc;
  
  // Status indicators
  --success-color: #4caf50;
  --warning-color: #ff9800;
  --error-color: #f44336;
  --info-color: #2196f3;
  --success-bg: rgba(76, 175, 80, 0.1);
  --warning-bg: rgba(255, 152, 0, 0.1);
  --error-bg: rgba(244, 67, 54, 0.1);
  --info-bg: rgba(33, 150, 243, 0.1);
  
  // Scrollbars
  --scrollbar-thumb: rgba(101, 102, 205, 0.3);
  --scrollbar-thumb-hover: rgba(101, 102, 205, 0.5);
  
  // Create Agent page (Light Theme)
  --create-agent-title: #000;
  --create-agent-card-bg: linear-gradient(102.14deg, rgba(255, 255, 255, 0.65) 1.07%, rgba(255, 255, 255, 0.7) 98.01%);
  --create-agent-card-border: rgba(255, 255, 255, 0.5);
  --create-agent-card-shadow: 0 8px 32px rgba(0, 0, 0, 0.07), 0 0 10px rgba(255, 255, 255, 0.3);
  --create-agent-card-glow: linear-gradient(120deg, rgba(255, 255, 255, 0.7), rgba(255, 255, 255, 0.3), rgba(255, 255, 255, 0.7));
  --create-agent-card-hover-bg: linear-gradient(102.14deg, rgba(255, 255, 255, 0.7) 1.07%, rgba(255, 255, 255, 0.75) 98.01%);
  --create-agent-card-hover-border: rgba(255, 255, 255, 0.6);
  --create-agent-card-hover-shadow: 0 12px 40px rgba(0, 0, 0, 0.12), 0 0 15px rgba(255, 255, 255, 0.4);
  --create-agent-type-title: #000;
  --create-agent-type-title-shadow: 0 1px 3px rgba(255, 255, 255, 0.6);
  --create-agent-type-desc: #292C3D;
  --create-agent-type-desc-shadow: 0 1px 2px rgba(255, 255, 255, 0.3);
  --create-agent-fallback-bg: rgba(255, 255, 255, 0.9);
  --create-agent-fallback-hover-bg: rgba(255, 255, 255, 0.95);
  
  // Individual Agent Component (Light Theme)
  --agent-card-bg: #FFFFFF;
  --agent-card-border: rgba(230, 230, 230, 0.8);
  --agent-card-shadow: rgba(0, 0, 0, 0.05);
  --agent-template-card-border: #e0e0e0;
  --agent-template-card-hover-bg: rgba(101, 102, 205, 0.05);
  --agent-template-card-hover-border: #6566CD;
  --agent-template-card-hover-shadow: rgba(0, 0, 0, 0.05);
  --agent-action-btn-bg: #f5f5f5;
  --agent-action-btn-border: #e0e0e0;
  --agent-action-btn-hover-bg: rgba(101, 102, 205, 0.05);
  --agent-action-btn-hover-border: #6566CD;
  --agent-gradient-border-bg: white;
  --agent-analyze-btn-hover: 0.9;
  --agent-chat-column-bg: #f9f9f9;
  --agent-chat-column-border: #eaeaea;
  --agent-chat-column-shadow: rgba(0, 0, 0, 0.05);
  --agent-data-card-bg: #f5f5f5;
  --agent-data-card-border: #e0e0e0;
  --agent-data-card-shadow: rgba(0, 0, 0, 0.05);
  --agent-data-card-hover-shadow: rgba(0, 0, 0, 0.1);
  --agent-data-card-hover-border: #d0d0d0;
  --agent-tag-bg: #f1f1f1;
  --agent-tag-text: #555;
  --agent-data-card-date: #777;
  --agent-tools-empty-bg: #F8F8F9;
  --agent-tools-empty-border: #E0E0E0;
  
  // Chat Interface (Light Theme)
  --chat-user-message-bg: #f8d7e3;
  --chat-user-message-text: #333333;
  --chat-ai-message-bg: #f0f2f5;
  --chat-ai-message-text: #333333;
  --chat-message-shadow: rgba(0, 0, 0, 0.05);
  --chat-input-bg: #f9fafb;
  --chat-input-shadow: rgba(0, 0, 0, 0.03);
  --chat-input-disabled-bg: #f7f7f7;
  --chat-typing-dot: #9ca3af;
  
  // Tools Card Component (Light Theme)
  --tools-card-bg: #f5f5f5;
  --tools-card-border: #e0e0e0;
  --tools-card-shadow: rgba(0, 0, 0, 0.05);
  --tools-card-hover-shadow: rgba(0, 0, 0, 0.1);
  --tools-card-hover-border: #d0d0d0;
  --tools-card-title: #333;
  --tools-card-tag-bg: #eeeeee;
  --tools-card-tag-text: #555;
  --tools-card-date: #777;
  --tools-card-action-hover-bg: #f5f5f5;
  --tools-card-action-hover-color: #6566CD;
  --tools-card-delete-hover-color: #FF4757;
  
  // Knowledge base components (Light Theme)
  --agent-retriever-btn-bg: #f8f9fa;
  --agent-retriever-btn-text: #333;
  --agent-retriever-btn-border: #e0e0e0;
  --agent-retriever-btn-hover-bg: #f0f0f0;
  --agent-retriever-btn-hover-border: #d0d0d0;
  --agent-retriever-btn-selected-bg: #E6E4FB;
  --agent-retriever-btn-selected-text: #6566CD;
  --agent-retriever-btn-selected-border: #6566CD;
  
  // Slider components (Light Theme)
  --agent-slider-bg: #e0e0e0;
  --agent-slider-thumb-bg: #6566CD;
  --agent-slider-thumb-shadow: none;
  --agent-slider-input-bg: #ffffff;
  --agent-slider-input-border: #e0e0e0;
  --agent-slider-input-text: #333333;
  
  // Table components (Light Theme)
  --agent-table-header-bg: #f8f8f9;
  --agent-table-header-text: #333333;
  --agent-table-border: #e0e0e0;
  --agent-table-cell-text: #666666;
  --agent-table-row-hover: #f5f5fa;
  
  // Button styling variables (Light Theme)
  --button-start-gradient: #6566CD;
  --button-end-gradient: #F96CAB;
  --button-gradient: linear-gradient(90deg, var(--button-start-gradient) 0%, var(--button-end-gradient) 100%);
  --edit-enhance-button-bg: rgba(255, 255, 255, 0.95);
  --edit-enhance-button-bg-hover: rgba(255, 255, 255, 0.98);
  --edit-enhance-button-shadow: rgba(0, 0, 0, 0.05);
  --edit-enhance-button-shadow-hover: rgba(0, 0, 0, 0.1);
  --svg-stroke-url: url(#gradient);
  --edit-field-bg: rgba(248, 248, 249, 0.95);


  --agent-node-bg: white;

  // Table Grid component ( Light Theme )
  --table-grid-border-color: #858AAD;
  --table-grid-background-color: #ffffff;
  --table-grid-shadow-color: rgba(0, 0, 0, 0.1);
  --table-grid-header-background: #5082EF;
  --table-grid-header-text-color: #ffffff;
  --table-grid-header-border-color: #e5e7eb;
  --table-grid-body-text-color: #333333;
  --table-grid-row-border-color: #f1f3f4;
  --table-grid-row-hover-background: #e3f2fd;
  --table-grid-empty-text-color: #6c757d; 
}

.dark-theme {
  // Dark Theme Variables
  
  // Core colors
  --background-color: #121212;
  --text-color: #e0e0e0;
  --text-secondary: #a0a0a0;
  
  // Background image
  --bg-image: var('assets/images/bg-dark.svg');
  
  // Glass effect
  --glass-bg: linear-gradient(102.14deg, rgba(30, 30, 35, 0.7) 1.07%, rgba(40, 40, 45, 0.7) 98.01%);
  --glass-border: rgba(80, 80, 95, 0.2);
  --glass-shadow: rgba(0, 0, 0, 0.15);
  
  // Branding colors remain mostly the same - branding is consistent across themes
  --primary-start: #6566CD;
  --primary-end: #F96CAB;
  --gradient-primary: linear-gradient(90deg, var(--primary-start) 0%, var(--primary-end) 100%);
  
  // Dashboard specific colors for dark theme
  --dashboard-primary: #6566CD;
  --dashboard-secondary: #F96CAB;
  --dashboard-text-primary: #e0e0e0;
  --dashboard-text-secondary: #a0a0a0;
  --dashboard-text-kpi-title: #a0a0d0;
  --dashboard-text-kpi-value: #c0c0e0;
  --dashboard-border-light: #2a2a35;
  --dashboard-bg-light: #1a1a1a;
  --dashboard-bg-lighter: #1e1e1e;
  --dashboard-bg-action-button: #1e1e1e;
  --dashboard-bg-icon-button: #2a2a3d;
  --dashboard-quick-action-button-bg: var(--gradient-primary);
  --dashboard-quick-action-text: #ffffff;
  --dashboard-text-welcome: #c0c0e0;
  --dashboard-shadow-color: rgba(0, 0, 0, 0.2);
  --dashboard-shadow-hover: rgba(101, 102, 205, 0.25);
  --dashboard-shadow-medium: rgba(0, 0, 0, 0.25);
  --dashboard-shadow-strong: rgba(0, 0, 0, 0.3);
  --dashboard-border-accent: rgba(101, 102, 205, 0.3);
  --dashboard-action-button-border: #333340;
  --dashboard-card-bg: #1e1e24;
  --dashboard-scrollbar-track: #2a2a2a;
  --dashboard-scrollbar-thumb: rgba(101, 102, 205, 0.25);
  --dashboard-scrollbar-thumb-hover: rgba(101, 102, 205, 0.4);
  --dashboard-scrollbar-thumb-border: rgba(101, 102, 205, 0.1);
  --dashboard-toggle-stroke: #8b8dda;
  --dashboard-toggle-stroke-collapsed: #e0e0e0;
  --dashboard-fading-border: rgba(60, 60, 70, 0.6);
  --dashboard-table-row-border: rgba(60, 60, 70, 0.6);
  --dashboard-copyright: #8e8e9a;
  --dashboard-approve-button-bg: #1b3d38;
  --dashboard-approve-button-color: #a0dfd0;
  --dashboard-approve-button-border: #02a080;
  --dashboard-approve-button-shadow: rgba(2, 160, 128, 0.2);
  
  // Component colors
  --card-bg: linear-gradient(102.14deg, rgba(30, 30, 35, 0.7) 1.07%, rgba(40, 40, 45, 0.7) 98.01%);
  --card-border: rgba(70, 70, 80, 0.2);
  --card-shadow: rgba(0, 0, 0, 0.2);
  --card-hover-shadow: rgba(0, 0, 0, 0.3);
  --card-hover-border: rgba(138, 140, 180, 0.4);
  --card-border-radius: 12px;
  --card-border-color: rgba(70, 70, 80, 0.2);
  --card-box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
  --card-header-bg: rgba(30, 30, 35, 0.5);
  --card-header-color: var(--text-color);
  --card-body-bg: transparent;
  --card-body-color: var(--text-color);
  --card-footer-bg: rgba(30, 30, 35, 0.5);
  --card-footer-color: var(--text-color);
  
  // Status indicators for dark theme
  --status-active-color: #c0c0e0;
  --status-active-border: #01d0a0;
  --status-inactive-bg: rgba(231, 76, 60, 0.2);
  --status-inactive-color: #e77c6c;
  --status-pending-bg: rgba(241, 196, 15, 0.2);
  --status-pending-color: #f1d85f;
  
  // DataCard specific
  --tag-bg: rgba(40, 40, 50, 0.6);
  --tag-text: #e0e0e0;
  --tag-border: rgba(70, 70, 80, 0.3);
  --subtag-bg: rgba(35, 35, 45, 0.6);
  --subtag-text: #a0a0a0;
  --subtag-border: rgba(60, 60, 70, 0.3);
  --action-icon-color: #a0a0b8;
  --action-button-hover: rgba(60, 60, 70, 0.7);
  --action-button-hover-color: #e0e0e0;
  --view-icon-color: #5a9ee8;
  --edit-icon-color: #7ccc5a;
  --delete-icon-color: #e86a5a;
  
  // Code styling
  --code-bg: #0f172a;
  --code-color: #e2e8f0;
  --code-scrollbar-track: rgba(255, 255, 255, 0.05);
  --code-scrollbar-thumb: rgba(255, 255, 255, 0.2);
  --code-scrollbar-thumb-hover: rgba(255, 255, 255, 0.3);
  
  // Modal
  --modal-bg: rgba(30, 30, 35, 0.95);
  --modal-border: rgba(70, 70, 80, 0.3);
  --modal-shadow: rgba(0, 0, 0, 0.4);
  --modal-header-border: rgba(70, 70, 80, 0.3);
  --modal-footer-border: rgba(70, 70, 80, 0.3);
  --modal-backdrop: rgba(0, 0, 0, 0.7);
  
  // Dropdown components for dark theme
  --dropdown-bg: #1e1e24;
  --dropdown-text: #e0e0e0;
  --dropdown-title-color: #ffffff;
  --dropdown-desc-color: #a0a0a0;
  --dropdown-border: #3d3d3d;
  --dropdown-shadow: rgba(0, 0, 0, 0.3);
  --dropdown-hover-bg: #292c3d;
  --dropdown-selected-bg: #323552;
  --dropdown-selected-text: #8b8dda;
  --dropdown-item-hover-text: #ffffff;
  --dropdown-item-desc-hover: #c0c0e0;
  --dropdown-menu-border: rgba(101, 102, 205, 0.2);
  --dropdown-placeholder: #8a8a8a;
  --dropdown-focus-border: #6566CD;
  --dropdown-focus-shadow: rgba(101, 102, 205, 0.3);
  --dropdown-required-border: #FF5a67;
  --dropdown-checkbox-bg: #6566CD;
  --dropdown-checkbox-border: #3d3d3d;
  --dropdown-disabled-bg: #252525;
  
  // CreateCard specific
  --create-icon-bg: rgba(102, 109, 153, 0.2);
  --create-icon-color: #a0a0b8;
  --create-icon-hover-bg: rgba(102, 109, 153, 0.3);
  --create-label-color: #a0a0b8;
  
  // Form elements
  --input-bg: #2a2a2a;
  --input-border: #3d3d3d;
  --input-text: #e0e0e0;
  --input-placeholder: #8a8a8a;
  --input-focus-border: #6566CD;
  --input-focus-shadow: rgba(101, 102, 205, 0.25);
  --form-label-color: #b0b0b0;
  --form-input-bg: #2a2a2a;
  --form-input-color: #e0e0e0;
  --form-input-border: #3d3d3d;
  --form-input-placeholder: #8a8a8a;
  --form-input-focus-border: #6566CD;
  --form-input-focus-shadow: rgba(101, 102, 205, 0.25);
  
  // Navigation
  --nav-bg: transparent;
  --nav-text: #e0e0e0;
  --nav-hover: transparent;
  --nav-active: #292c3d;
  --nav-active-text: #8b8dda;
  --nav-border: #333333;
  --nav-item-color: #e0e0e0;
  --nav-item-active-color: #8b8dda;
  --nav-item-active-bg: #292c3d;

  // Navigation Pills (Dark Theme)
  --nav-pills-bg: rgba(40, 44, 61, 0.9);
  --nav-pills-shadow:  0 2px 8px #3d3f41;
  --nav-pill-hover-bg: linear-gradient(118deg, #7fc2eb, #5b92ea 89.27%);
  --nav-pill-hover-color: #ffffff;
  --nav-pill-hover-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  --nav-pill-hover-icon-filter: brightness(0) invert(1);
  --nav-pill-selected-bg: linear-gradient(118deg, #7fc2eb, #5b92ea 95.27%);
  --nav-pill-selected-color: #ffffff;
  --nav-pill-selected-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  --nav-pill-selected-icon-filter: brightness(0) invert(1);
  
  // Buttons
  --button-primary-bg: var(--gradient-primary);
  --button-primary-text: #ffffff;
  --button-secondary-bg: transparent;
  --button-secondary-border: #8b8dda;
  --button-secondary-text: #8b8dda;
  --button-secondary-hover-bg: rgba(139, 141, 218, 0.1);
  --button-secondary-hover-border: #a5a7e6;
  --button-secondary-hover-text: #a5a7e6;
  --button-secondary-active-bg: rgba(139, 141, 218, 0.2);
  --button-hover-opacity: 0.8;
  
  // Data display
  --table-header-bg: #1a1c25;
  --table-border: #292c3d;
  --table-row-hover: #292c3d;
  --table-header-text: #e0e0e0;
  --table-text: #b0b0b0;
  --table-stripe-bg: #1d1f2a;
  
  // Status indicators
  --success-color: #66bb6a;
  --warning-color: #ffa726;
  --error-color: #ef5350;
  --info-color: #42a5f5;
  --success-bg: rgba(102, 187, 106, 0.15);
  --warning-bg: rgba(255, 167, 38, 0.15);
  --error-bg: rgba(239, 83, 80, 0.15);
  --info-bg: rgba(66, 165, 245, 0.15);
  
  // Scrollbars
  --scrollbar-thumb: rgba(246, 59, 143, 0.4);
  --scrollbar-thumb-hover: rgba(246, 59, 143, 0.6);
  
  // Create Agent page (Dark Theme)
  --create-agent-title: #e0e0e0;
  --create-agent-card-bg: linear-gradient(102.14deg, rgba(30, 30, 40, 0.75) 1.07%, rgba(40, 40, 55, 0.8) 98.01%);
  --create-agent-card-border: rgba(80, 80, 120, 0.3);
  --create-agent-card-shadow: 0 8px 32px rgba(0, 0, 0, 0.25), 0 0 10px rgba(80, 80, 110, 0.2);
  --create-agent-card-glow: linear-gradient(120deg, rgba(101, 102, 205, 0.4), rgba(80, 80, 120, 0.2), rgba(101, 102, 205, 0.4));
  --create-agent-card-hover-bg: linear-gradient(102.14deg, rgba(35, 35, 50, 0.8) 1.07%, rgba(45, 45, 65, 0.85) 98.01%);
  --create-agent-card-hover-border: rgba(101, 102, 205, 0.4);
  --create-agent-card-hover-shadow: 0 12px 40px rgba(0, 0, 0, 0.4), 0 0 15px rgba(101, 102, 205, 0.25);
  --create-agent-type-title: #e0e0e0;
  --create-agent-type-title-shadow: 0 1px 3px rgba(0, 0, 0, 0.6);
  --create-agent-type-desc: #b0b0c0;
  --create-agent-type-desc-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
  --create-agent-fallback-bg: rgba(30, 30, 40, 0.9);
  --create-agent-fallback-hover-bg: rgba(35, 35, 50, 0.95);
  
  // Individual Agent Component (Dark Theme)
  --agent-card-bg: transparent;
  --agent-card-border: rgba(60, 60, 80, 0.3);
  --agent-card-shadow: rgba(0, 0, 0, 0.2);
  --agent-template-card-border: #333340;
  --agent-template-card-hover-bg: rgba(101, 102, 205, 0.15);
  --agent-template-card-hover-border: #6566CD;
  --agent-template-card-hover-shadow: rgba(0, 0, 0, 0.2);
  --agent-action-btn-bg: #2a2a35;
  --agent-action-btn-border: #3d3d45;
  --agent-action-btn-hover-bg: rgba(101, 102, 205, 0.15);
  --agent-action-btn-hover-border: #6566CD;
  --agent-gradient-border-bg: #1e1e24;
  --agent-analyze-btn-hover: 0.8;
  --agent-chat-column-bg: #252530;
  --agent-chat-column-border: #3d3d45;
  --agent-chat-column-shadow: rgba(0, 0, 0, 0.25);
  --agent-data-card-bg: #252530;
  --agent-data-card-border: #3d3d45;
  --agent-data-card-shadow: rgba(0, 0, 0, 0.15);
  --agent-data-card-hover-shadow: rgba(0, 0, 0, 0.3);
  --agent-data-card-hover-border: #6566CD;
  --agent-tag-bg: #333340;
  --agent-tag-text: #b0b0c0;
  --agent-data-card-date: #999;
  --agent-tools-empty-bg: #252530;
  --agent-tools-empty-border: #3d3d45;
  
  // Chat Interface (Dark Theme)
  --chat-user-message-bg: #4c1d95;
  --chat-user-message-text: #f3f4f6;
  --chat-ai-message-bg: #292c3d;
  --chat-ai-message-text: #e0e0e0;
  --chat-message-shadow: rgba(0, 0, 0, 0.2);
  --chat-input-bg: #252530;
  --chat-input-shadow: rgba(0, 0, 0, 0.15);
  --chat-input-disabled-bg: #1e1e24;
  --chat-typing-dot: #8b8dda;
  
  // Tools Card Component (Dark Theme)
  --tools-card-bg: #252530;
  --tools-card-border: #3d3d45;
  --tools-card-shadow: rgba(0, 0, 0, 0.15);
  --tools-card-hover-shadow: rgba(101, 102, 205, 0.2);
  --tools-card-hover-border: rgba(101, 102, 205, 0.4);
  --tools-card-title: #e0e0e0;
  --tools-card-tag-bg: #333340;
  --tools-card-tag-text: #b0b0c0;
  --tools-card-date: #999;
  --tools-card-action-hover-bg: #2a2a35;
  --tools-card-action-hover-color: #8b8dda;
  --tools-card-delete-hover-color: #ff6b76;
  
  // Knowledge base components (Dark Theme)
  --agent-retriever-btn-bg: #252530;
  --agent-retriever-btn-text: #b0b0c0;
  --agent-retriever-btn-border: #3d3d45;
  --agent-retriever-btn-hover-bg: #2a2a35;
  --agent-retriever-btn-hover-border: rgba(101, 102, 205, 0.4);
  --agent-retriever-btn-selected-bg: #292c3d;
  --agent-retriever-btn-selected-text: #8b8dda;
  --agent-retriever-btn-selected-border: #6566CD;
  
  // Slider components (Dark Theme)
  --agent-slider-bg: #3d3d45;
  --agent-slider-thumb-bg: #6566CD;
  --agent-slider-thumb-shadow: 0 0 3px rgba(101, 102, 205, 0.5);
  --agent-slider-input-bg: #2a2a35;
  --agent-slider-input-border: #3d3d45;
  --agent-slider-input-text: #e0e0e0;
  
  // Table components (Dark Theme)
  --agent-table-header-bg: #252530;
  --agent-table-header-text: #b0b0c0;
  --agent-table-border: #333340;
  --agent-table-cell-text: #b0b0c0;
  --agent-table-row-hover: #2a2a35;
  
  // Additional component colors for dark theme
  --agent-enhance-btn-bg: rgba(40, 40, 50, 0.95);
  --agent-enhance-btn-border-gradient: linear-gradient(90deg, #6566CD 50%, #F96CAB 100%);
  --agent-enhance-btn-text-gradient: linear-gradient(90deg, #8b8dda 30%, #fc85bc 100%);
  --agent-enhance-btn-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  --agent-enhance-btn-hover-shadow: 0 2px 6px rgba(101, 102, 205, 0.3);
  
  --agent-edit-btn-bg: rgba(40, 40, 50, 0.95);
  --agent-edit-btn-border-gradient: linear-gradient(90deg, #6566CD 50%, #F96CAB 100%);
  --agent-edit-btn-text-gradient: linear-gradient(90deg, #8b8dda 30%, #fc85bc 100%);
  --agent-edit-btn-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  --agent-edit-btn-hover-shadow: 0 2px 6px rgba(101, 102, 205, 0.3);
  
  // Button styling variables (Dark Theme)
  --button-start-gradient: #8b8dda;
  --button-end-gradient: #fc85bc;
  --button-gradient: linear-gradient(90deg, var(--button-start-gradient) 0%, var(--button-end-gradient) 100%);
  --edit-enhance-button-bg: rgba(40, 40, 50, 0.95);
  --edit-enhance-button-bg-hover: rgba(45, 45, 60, 0.98);
  --edit-enhance-button-shadow: rgba(0, 0, 0, 0.2);
  --edit-enhance-button-shadow-hover: rgba(101, 102, 205, 0.2);
  --svg-stroke-url: url(#gradient);
  --edit-field-bg: rgba(40, 40, 50, 0.95);

  --agent-node-bg: rgb(24 24 36);
} 