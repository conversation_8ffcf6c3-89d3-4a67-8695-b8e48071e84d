// Base button styles
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  border-radius: 8px;
  font-weight: 500;
  transition: all 0.3s ease;
  cursor: pointer;
  font-family: inherit;
  white-space: nowrap;
  text-align: center;
  border: none;
  position: relative;
  overflow: hidden;

  &:focus {
    outline: none;
    box-shadow: 0 0 0 3px var(--input-focus-shadow);
  }
  
  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
}

// Button sizes
.btn-small {
  padding: 6px 16px;
  font-size: 14px;
  height: 36px;
  
  .button-icon {
    width: 16px;
    height: 16px;
  }
}

.btn-medium {
  padding: 10px 24px;
  font-size: 16px;
  height: 44px;
  
  .button-icon {
    width: 18px;
    height: 18px;
  }
}

.btn-large {
  padding: 12px 32px;
  font-size: 18px;
  height: 52px;
  
  .button-icon {
    width: 20px;
    height: 20px;
  }
}

// Button variants
.btn-primary {
  background: var(--button-primary-bg);
  color: var(--button-primary-text);
  
  &:hover:not(:disabled) {
    box-shadow: 0 4px 12px rgba(101, 102, 205, 0.3);
    transform: translateY(-1px);
    opacity: var(--button-hover-opacity);
  }
  
  &:active:not(:disabled) {
    transform: translateY(0);
    opacity: 1;
  }
}

.btn-secondary {
  background: var(--button-secondary-bg);
  color: var(--button-secondary-text);
  border: 1px solid var(--button-secondary-border);
  
  &:hover:not(:disabled) {
    background: var(--button-secondary-hover-bg);
    border-color: var(--button-secondary-hover-border);
    color: var(--button-secondary-hover-text);
  }
  
  &:active:not(:disabled) {
    background: var(--button-secondary-active-bg);
    transform: translateY(1px);
  }
}

.btn-outline {
  background: transparent;
  color: var(--primary-start);
  border: 1px solid var(--primary-start);
  
  &:hover:not(:disabled) {
    background: var(--button-secondary-hover-bg);
    border-color: var(--primary-start);
  }
  
  &:active:not(:disabled) {
    background: var(--button-secondary-active-bg);
    transform: translateY(1px);
  }
}

.btn-text {
  background: transparent;
  color: var(--text-color);
  padding-left: 8px;
  padding-right: 8px;
  
  &:hover:not(:disabled) {
    background: var(--nav-hover);
    color: var(--primary-start);
  }
  
  &:active:not(:disabled) {
    background: var(--nav-active);
    transform: translateY(1px);
  }
}

.btn-danger {
  background: var(--error-color);
  color: white;
  
  &:hover:not(:disabled) {
    opacity: var(--button-hover-opacity);
    box-shadow: 0 4px 12px rgba(255, 59, 48, 0.3);
  }
  
  &:active:not(:disabled) {
    opacity: 1;
    transform: translateY(1px);
  }
}

// Button states
.btn-full-width {
  width: 100%;
}

// Ensure button content doesn't wrap
.btn .button-content {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.btn-loading {
  color: transparent !important;
  pointer-events: none;
  
  .spinner {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
  }
  
  .button-icon {
    opacity: 0;
  }
}

// Button with icon
.btn-with-icon {
  .button-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    
    img {
      width: 100%;
      height: 100%;
      object-fit: contain;
    }
  }
  
  &.icon-left {
    .button-icon.left {
      margin-right: 4px;
    }
  }
  
  &.icon-right {
    .button-content {
      order: -1;
    }
    
    .button-icon.right {
      margin-left: 4px;
    }
  }
}

// Loading spinner
.spinner {
  display: flex;
  align-items: center;
  justify-content: center;
}

.spinner-circle {
  width: 20px;
  height: 20px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top-color: white;
  border-radius: 50%;
  animation: spin 0.8s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

// Theme transitions
.btn {
  transition: all 0.3s ease, background 0.2s ease, color 0.2s ease, border 0.2s ease, opacity 0.2s ease;
}

.spinner-circle {
  border-color: rgba(255, 255, 255, 0.3);
  border-top-color: white;
}

/* Responsive adjustments */
@media (max-width: 576px) {
  .btn-medium {
    padding: 8px 20px;
    font-size: 15px;
    height: 40px;
  }
  
  .btn-large {
    padding: 10px 24px;
    font-size: 16px;
    height: 48px;
  }
} 