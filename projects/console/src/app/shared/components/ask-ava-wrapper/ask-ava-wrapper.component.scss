.header-section {
  margin-bottom: 1rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.askava-container {
  width: 100%;
}

.place-end {
  display: flex;
  justify-content: flex-end;
}

.place-start {
  display: flex;
  justify-content: flex-start;
}

.place-space-between {
  display: flex;
  justify-content: space-between;
}

.actons {
  margin-top: 1rem;
  align-items: center;
}

.askava-container {
  // Overrding text aligns for the label
  &::ng-deep {
    label {
      text-align: start !important;
    }
  }
}

.generated-output__body {
  display: flex;
  flex-direction: column;
  row-gap: 1rem;
}


// Monaco editor inside pop was causing code to be placed on center 
// so fixing issue by overriding CSS
#outputEditor::ng-deep {
  .monaco-editor .view-lines {
    justify-content: flex-start !important;
    text-align: left !important;
  }
  .monaco-editor .sticky-scroll {
    justify-content: flex-start !important;
    text-align: left !important;
    left: 0 !important;
  }
  .monaco-editor .sticky-widget-lines-scrollable {
    left: 85px !important;
    text-align: start;
  }
}
// --------------------------------------------------