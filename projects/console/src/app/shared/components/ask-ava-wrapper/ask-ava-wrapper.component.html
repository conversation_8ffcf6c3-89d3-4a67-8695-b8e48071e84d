<ava-popup [show]="_show" [title]="labels.askAva" popupWidth="60vw" [showHeaderIcon]="false" iconColor="#dc3545"
    [confirmButtonBackground]="'#dc3545'" [showInlineMessage]="false"  message="" (closed)="handleClose()">
    <section class="askava-container">

        <div class="prompt-container">
            <ava-textarea [formControl]="prompt" [id]="labels.textAreaRequirment" [label]="labels.textAreaRequirment"
                placeholder="{{labels.textAreaRequirment}}" [rows]="4" size="md" [fullWidth]="true" [required]="true">
            </ava-textarea>

            <div class="actons place-end">
                <ava-button label="{{ labels.reset }}" variant="secondary" size="small" (userClick)="onReset()">
                </ava-button>
                <ng-container>
                    <ava-button [disabled]="!prompt.value" *ngIf="!isLoading; else spinnerRef"
                        label="{{ labels.generate }}" variant="primary" size="small" (userClick)="onClickGenerate()">
                    </ava-button>
                    <ng-template #spinnerRef>
                        <ava-spinner size="sm"></ava-spinner>
                    </ng-template>
                </ng-container>
            </div>
        </div>

        <div class="generated-output" *ngIf="showOutput">
            <ng-content></ng-content>

            <!-- <div class="place-start">
                <h2>Output Generated</h2>
            </div>

            <div class="generated-output__body">
                <div class="row">
                    <div class="col-6">
                        <ava-textbox [readonly]="true" [formControl]="getOutputControl('name')"
                            [label]="labels.toolName"></ava-textbox>
                    </div>
                    <div class="col-6">
                        <ava-textbox [readonly]="true" [label]="labels.toolClassName"
                            [formControl]="getOutputControl('description')"></ava-textbox>
                    </div>
                </div>
                <ava-textarea [readonly]="true" [rows]="2" [label]="labels.description"
                    [formControl]="getOutputControl('toolClassName')"></ava-textarea>
                <div>
  

                    <app-code-editor id="outputEditor" #codeEditor title="{{ labels.toolClassDefinition }}"
                        language="python" [value]="getOutputControl('classDefinition').value"
                        customCssClass="tools-monaco-editor" footerText="{{ labels.note }}"
                        [readonly]="isFieldsDisabled">
                    </app-code-editor>
                </div>



            </div> -->

            <div class="actons place-end">
                <ava-button label="{{ labels.cancel }}" variant="secondary" size="small" (userClick)="onCancle()">
                </ava-button>
                <ava-button label="{{ labels.use }}" variant="primary" size="small" (userClick)="onUse()">
                </ava-button>
            </div>

        </div>
    </section>
</ava-popup>