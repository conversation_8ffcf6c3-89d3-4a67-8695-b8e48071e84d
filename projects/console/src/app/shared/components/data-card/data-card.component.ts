import { Component, Input, Output, EventEmitter } from '@angular/core';
import { CommonModule } from '@angular/common';
import { CardComponent } from '../card/card.component';
import { CardData, CardAction } from '../../models/card.model';

@Component({
  selector: 'app-data-card',
  standalone: true,
  imports: [CommonModule, CardComponent],
  templateUrl: './data-card.component.html',
  styleUrl: './data-card.component.scss'
})
export class DataCardComponent {
  @Input() data!: CardData;
  @Output() actionClicked = new EventEmitter<{action: string, cardId: string}>();
  @Output() cardClicked = new EventEmitter<string>();

  onActionClick(event: Event, action: CardAction): void {
    event.stopPropagation(); // Prevent card click when action is clicked
    this.actionClicked.emit({
      action: action.action,
      cardId: this.data.id
    });
  }

  onCardClick(): void {
    this.cardClicked.emit(this.data.id);
  }
}
