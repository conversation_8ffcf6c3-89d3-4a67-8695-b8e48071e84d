import {
  Component,
  Input,
  Output,
  EventEmitter,
  ViewChild,
  ElementRef,
  AfterViewInit,
  OnDestroy,
  OnChanges,
  OnInit,
  ChangeDetectorRef,
  ContentChild,
  TemplateRef,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormControl, ReactiveFormsModule } from '@angular/forms';
import {
  ButtonComponent,
  AvaTextboxComponent,
  AvaTextareaComponent,
  IconComponent,
} from '@ava/play-comp-library';

export interface CanvasNode {
  id: string;
  type: string;
  data: any;
  position: { x: number; y: number };
}

export interface CanvasEdge {
  id: string;
  source: string;
  target: string;
  animated?: boolean;
  pathData?: string; // Pre-calculated SVG path data
}

export interface SelectOption {
  value: string;
  label: string;
}

export interface CustomInputField {
  id: string;
  label: string;
  type: 'text' | 'textarea' | 'select' | 'number' | 'email';
  placeholder?: string;
  required?: boolean;
  disabled?: boolean;
  options?: SelectOption[]; // For select type
  control?: FormControl;
  value?: any;
  width?: string;
  height?: string;
}

export interface MetadataConfig {
  enabled: boolean;
  orgOptions: SelectOption[];
  domainOptions: SelectOption[];
  projectOptions: SelectOption[];
  teamOptions: SelectOption[];
  orgControl: FormControl;
  domainControl: FormControl;
  projectControl: FormControl;
  teamControl: FormControl;
}

interface TempConnection {
  isActive: boolean;
  sourceNodeId?: string;
  targetNodeId?: string;
  sourceHandleType?: 'source' | 'target';
  sourceX?: number;
  sourceY?: number;
  targetX?: number;
  targetY?: number;
}

type CanvasToolMode = 'select' | 'pan' | 'zoom' | 'disabled';

interface CanvasViewport {
  zoom: number;
  x: number;
  y: number;
  isDragging: boolean;
  lastMouseX: number;
  lastMouseY: number;
}

interface NodeConnectionPoints {
  [nodeId: string]: {
    top: { x: number; y: number };
    right: { x: number; y: number };
    bottom: { x: number; y: number };
    left: { x: number; y: number };
  };
}

@Component({
  selector: 'app-canvas-board',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    ButtonComponent,
    AvaTextboxComponent,
    AvaTextareaComponent,
    IconComponent,
  ],
  templateUrl: './canvas-board.component.html',
  styleUrls: ['./canvas-board.component.scss'],
})
export class CanvasBoardComponent
  implements OnInit, AfterViewInit, OnDestroy, OnChanges
{
  @ViewChild('canvasContainer') canvasContainer!: ElementRef;
  @ContentChild('nodeTemplate') nodeTemplate!: TemplateRef<any>;

  // Inputs
  @Input() nodes: CanvasNode[] = [];
  @Input() edges: CanvasEdge[] = [];
  @Input() showGrid: boolean = true;
  @Input() enablePan: boolean = false; // Disabled by default
  @Input() enableZoom: boolean = false; // Disabled by default
  @Input() enableConnections: boolean = true;
  @Input() minZoom: number = 0.1;
  @Input() maxZoom: number = 2;
  @Input() connectionColor: string = '#9ca3af'; // Grey color for connections
  @Input() fallbackMessage: string = 'Drop items here to get started';
  @Input() navigationHints: string[] = [
    'Select toolbar options to enable canvas interactions',
    'Alt + Drag to pan canvas (when enabled)',
    'Mouse wheel to zoom (when enabled)',
    'Space to reset view',
  ];
  @Input() showToolbar: boolean = true;
  @Input() primaryButtonText: string = 'Execute';
  @Input() primaryButtonIcon: string = '';
  @Input() enableUndo: boolean = true;
  @Input() enableRedo: boolean = true;
  @Input() enableReset: boolean = true;
  @Input() showCanvasTools: boolean = true;
  @Input() enableGridToggle: boolean = true;
  @Input() enablePanMode: boolean = true;
  @Input() enableSelectionMode: boolean = true;
  @Input() enableZoomControls: boolean = true;
  @Input() isExecuteMode: boolean = false;
  @Input() showLeftActions: boolean = true;
  @Input() mouseInteractionsEnabled: boolean = true;

  // Built-in input fields configuration
  @Input() showHeaderInputs: boolean = false;
  @Input() inputFieldsConfig: {
    agentName?: {
      enabled: boolean;
      placeholder?: string;
      required?: boolean;
      style?: Record<string, any>;
    };
    agentType?: {
      enabled: boolean;
      options?: SelectOption[];
      defaultValue?: string;
    };
    agentTypeTag?: {
      enabled: boolean;
      value?: string;
      showInAgentsBuilderOnly?: boolean;
    };
    metadata?: {
      enabled: boolean;
      label?: string;
      statusText?: { saved: string; notSaved: string };
    };
    agentDetails?: {
      enabled: boolean;
      label?: string;
      namePlaceholder?: string;
      detailPlaceholder?: string;
      detailLabel?: string;
    };
  } = {};

  // Input properties for initial values
  @Input() initialAgentName: string = '';
  @Input() initialAgentDetails: string = '';
  @Input() initialMetadata: {
    org: string;
    domain: string;
    project: string;
    team: string;
  } = {
    org: '',
    domain: '',
    project: '',
    team: '',
  };

  // Built-in metadata dropdown state
  isMetadataDropdownOpen = false;
  metadataStatus = 'Metadata Information not saved';

  // Agent details dropdown state
  isAgentDetailsDropdownOpen = false;

  // Form controls for built-in inputs
  @Input() agentNameControl = new FormControl('');
  agentTypeControl = new FormControl('individual');
  agentTypeDisplayControl = new FormControl('individual');

  // Agent details form controls
  @Input() agentDetailNameControl = new FormControl('');
  @Input() agentDetailControl = new FormControl('');

  // Metadata form controls
  orgControl = new FormControl('');
  domainControl = new FormControl('');
  projectControl = new FormControl('');
  teamControl = new FormControl('');

  // Dropdown options
  dropdownValues: { [key: string]: SelectOption[] } = {
    org: [
      { value: 'ascendion', label: 'Ascendion' },
      { value: 'company2', label: 'Company 2' },
      { value: 'company3', label: 'Company 3' },
    ],
    domain: [],
    project: [],
    team: [],
  };

  // Outputs
  @Output() nodeAdded = new EventEmitter<CanvasNode>();
  @Output() nodeRemoved = new EventEmitter<string>();
  @Output() nodeMoved = new EventEmitter<{
    nodeId: string;
    position: { x: number; y: number };
  }>();
  @Output() nodeSelected = new EventEmitter<string>();
  @Output() nodeDoubleClicked = new EventEmitter<string>();

  @Output() connectionStarted = new EventEmitter<{
    nodeId: string;
    handleType: 'source' | 'target';
    event: MouseEvent;
  }>();
  @Output() connectionCreated = new EventEmitter<CanvasEdge>();
  @Output() canvasDropped = new EventEmitter<{
    event: DragEvent;
    position: { x: number; y: number };
  }>();
  @Output() viewportChanged = new EventEmitter<CanvasViewport>();
  @Output() undoAction = new EventEmitter<void>();
  @Output() redoAction = new EventEmitter<void>();
  @Output() resetAction = new EventEmitter<void>();
  @Output() primaryButtonClicked = new EventEmitter<void>();
  @Output() stateChanged = new EventEmitter<{
    nodes: CanvasNode[];
    edges: CanvasEdge[];
  }>();
  @Output() agentNameChanged = new EventEmitter<string>();
  @Output() agentTypeChanged = new EventEmitter<string>();
  @Output() metadataChanged = new EventEmitter<{
    org: string;
    domain: string;
    project: string;
    team: string;
  }>();
  @Output() agentDetailsChanged = new EventEmitter<{
    name: string;
    useCaseDetails: string;
  }>();

  // Internal state
  selectedNodeId: string | null = null;
  tempConnection: TempConnection = { isActive: false };
  nodeConnectionPoints: NodeConnectionPoints = {};
  viewport: CanvasViewport = {
    zoom: 1,
    x: 0,
    y: 0,
    isDragging: false,
    lastMouseX: 0,
    lastMouseY: 0,
  };

  // Performance optimization for connection updates
  private updateConnectionPointsFrame: number | null = null;

  // Internal history management
  private history: { nodes: CanvasNode[]; edges: CanvasEdge[] }[] = [];
  private historyIndex: number = -1;
  private maxHistorySize: number = 50;
  private isRestoringState: boolean = false;

  // Canvas tool states
  canvasMode: CanvasToolMode = 'select'; // Start with select mode
  showGridDots: boolean = true;

  // Mouse function controls
  // mouseInteractionsEnabled: boolean = true; // Enable mouse interactions by default

  // Toolbar is now fixed at bottom center - no drag state needed

  constructor(private cdr: ChangeDetectorRef) {}

  ngOnInit(): void {
    // Set initial values
    this.setInitialValues();

    // Subscribe to agent name changes
    this.agentNameControl.valueChanges.subscribe((value) => {
      if (value !== null && value !== undefined) {
        this.agentNameChanged.emit(value);
      }
    });

    // Subscribe to agent type changes
    this.agentTypeControl.valueChanges.subscribe((value) => {
      if (value !== null && value !== undefined) {
        this.agentTypeChanged.emit(value);
      }
    });
  }

  private setInitialValues(): void {
    // Set agent name
    if (this.initialAgentName) {
      this.agentNameControl.setValue(this.initialAgentName, {
        emitEvent: false,
      });
    }

    // Set agent details
    if (this.initialAgentName) {
      this.agentDetailNameControl.setValue(this.initialAgentName, {
        emitEvent: false,
      });
    }
    if (this.initialAgentDetails) {
      this.agentDetailControl.setValue(this.initialAgentDetails, {
        emitEvent: false,
      });
    }

    // Set metadata
    if (this.initialMetadata) {
      this.orgControl.setValue(this.initialMetadata.org, { emitEvent: false });
      this.domainControl.setValue(this.initialMetadata.domain, {
        emitEvent: false,
      });
      this.projectControl.setValue(this.initialMetadata.project, {
        emitEvent: false,
      });
      this.teamControl.setValue(this.initialMetadata.team, {
        emitEvent: false,
      });
    }
  }

  // TrackBy function for nodes to maintain DOM element identity
  trackByNodeId(index: number, node: CanvasNode): string {
    return node.id;
  }

  ngOnChanges(changes: any): void {
    // Update initial values when inputs change
    if (
      changes['initialAgentName'] ||
      changes['initialAgentDetails'] ||
      changes['initialMetadata']
    ) {
      this.setInitialValues();
    }

    // Update internal state when inputs change (but not during state restoration)
    if (!this.isRestoringState) {
      if (changes.nodes && changes.nodes.currentValue) {
        console.log(
          '🔗 ngOnChanges - Received new nodes:',
          changes.nodes.currentValue.map((n: any) => ({
            id: n.id,
            position: n.position,
          })),
        );
        this.nodes = [...changes.nodes.currentValue];
      }
      if (changes.edges && changes.edges.currentValue) {
        console.log(
          '🔗 ngOnChanges - Received new edges:',
          changes.edges.currentValue.map((e: any) => ({
            id: e.id,
            source: e.source,
            target: e.target,
          })),
        );
        this.edges = [...changes.edges.currentValue];
      }

      // Update connection points when nodes change - IMMEDIATE
      if (changes.nodes) {
        console.log('🔗 ngOnChanges - Updating for node changes');
        this.updateNodeConnectionPoints();
        this.updateEdgePaths();
        // Force additional update after DOM settles
        setTimeout(() => this.updateNodeConnectionPoints(), 0);
      }

      if (changes.edges) {
        console.log('🔗 ngOnChanges - Updating for edge changes');
        this.updateEdgePaths();
      }
    }

    // Update connection points when execute mode changes
    if (changes['isExecuteMode']) {
      // Toolbar is now fixed at bottom center via CSS
      this.updateNodeConnectionPoints();
    }

    if (changes['mouseInteractionsEnabled']) {
      this.mouseInteractionsEnabled =
        changes['mouseInteractionsEnabled'].currentValue;
    }
  }

  ngAfterViewInit(): void {
    this.setupCanvasNavigation();
    // Use requestAnimationFrame for smooth initialization
    requestAnimationFrame(() => {
      this.updateNodeConnectionPoints();
      // Initialize history with current state
      this.saveToHistory();
      // Toolbar is now fixed at bottom center via CSS
      // Set initial cursor to default arrow
      this.setSelectMode();
    });
  }

  ngOnDestroy(): void {
    // Cleanup event listeners
    document.removeEventListener('keydown', this.handleKeyDown);

    // Cancel any pending animation frame
    if (this.updateConnectionPointsFrame) {
      cancelAnimationFrame(this.updateConnectionPointsFrame);
      this.updateConnectionPointsFrame = null;
    }
  }

  private setupCanvasNavigation(): void {
    const element = this.canvasContainer?.nativeElement;
    if (element) {
      document.addEventListener('keydown', this.handleKeyDown.bind(this));

      element.addEventListener('mouseup', () => {
        if (this.viewport.isDragging) {
          this.updateNodeConnectionPoints();
        }
      });
    }
  }

  private handleKeyDown = (event: KeyboardEvent): void => {
    if (event.key === ' ') {
      // Only reset viewport if the user is not typing in an input field
      const target = event.target as HTMLElement;
      const isInputField =
        target.tagName === 'INPUT' ||
        target.tagName === 'TEXTAREA' ||
        target.contentEditable === 'true' ||
        target.closest('ava-textbox') ||
        target.closest('ava-textarea') ||
        target.closest('.form-field') ||
        target.closest('.input-container') ||
        target.closest('.chat-input') ||
        target.closest('textarea') ||
        target.closest('input');

      if (!isInputField) {
        this.resetViewport();
        event.preventDefault();
      }
    }
  };

  resetViewport(): void {
    this.viewport = {
      zoom: 1,
      x: 0,
      y: 0,
      isDragging: false,
      lastMouseX: 0,
      lastMouseY: 0,
    };

    this.updateNodeConnectionPoints();
    this.viewportChanged.emit(this.viewport);
    this.cdr.detectChanges();
  }

  onDragOver(event: DragEvent): void {
    if (!this.enableConnections || !this.mouseInteractionsEnabled) return;
    event.preventDefault();
    if (event.dataTransfer) {
      event.dataTransfer.dropEffect = 'move';
    }
  }

  onDrop(event: DragEvent): void {
    if (!this.mouseInteractionsEnabled) return;
    event.preventDefault();

    // Save history before adding new node
    this.saveHistoryBeforeAction();

    const canvasBounds =
      this.canvasContainer.nativeElement.getBoundingClientRect();
    const position = {
      x:
        (event.clientX - canvasBounds.left - this.viewport.x) /
        this.viewport.zoom,
      y:
        (event.clientY - canvasBounds.top - this.viewport.y) /
        this.viewport.zoom,
    };

    const safePosition = {
      x: Math.max(0, position.x),
      y: Math.max(0, position.y),
    };

    this.canvasDropped.emit({ event, position: safePosition });
  }

  onNodeSelected(nodeId: string): void {
    this.selectedNodeId = nodeId;
    this.nodeSelected.emit(nodeId);
  }

  onNodeDoubleClicked(nodeId: string): void {
    this.nodeDoubleClicked.emit(nodeId);
  }

  onNodeMoved(data: {
    nodeId: string;
    position: { x: number; y: number };
  }): void {
    // Save history before moving node
    this.saveHistoryBeforeAction();

    // Update node position in internal state
    const nodeIndex = this.nodes.findIndex((node) => node.id === data.nodeId);
    if (nodeIndex !== -1) {
      this.nodes[nodeIndex] = {
        ...this.nodes[nodeIndex],
        position: data.position,
      };
    }

    // CRITICAL: Update edge paths immediately when node moves
    console.log('🔗 Node moved - updating edge paths for:', data.nodeId);
    this.updateEdgePaths();

    this.nodeMoved.emit(data);
    this.updateNodeConnectionPoints();

    // Force immediate change detection to update connections
    this.cdr.detectChanges();
  }

  onStartConnection(data: {
    nodeId: string;
    handleType: 'source' | 'target';
    event: MouseEvent;
  }): void {
    if (!this.enableConnections) return;

    const node = this.nodes.find((n) => n.id === data.nodeId);
    if (!node) return;

    // Enhanced connection starting - use mouse position on the node as starting point
    const canvasRect =
      this.canvasContainer.nativeElement.getBoundingClientRect();

    // Calculate the exact mouse position in canvas coordinates
    const mouseX =
      (data.event.clientX - canvasRect.left - this.viewport.x) /
      this.viewport.zoom;
    const mouseY =
      (data.event.clientY - canvasRect.top - this.viewport.y) /
      this.viewport.zoom;

    // Use the mouse position as the starting point for more natural connections
    let handleX = mouseX;
    let handleY = mouseY;

    // Get the node's connection points for fallback
    const connectionPoints = this.nodeConnectionPoints[data.nodeId];
    if (connectionPoints) {
      // If we have calculated connection points, use the nearest edge point
      const nodeElement = document.querySelector(
        `[data-node-id="${data.nodeId}"]`,
      ) as HTMLElement;
      if (nodeElement) {
        const rect = nodeElement.getBoundingClientRect();
        const nodeX =
          (rect.left - canvasRect.left - this.viewport.x) / this.viewport.zoom;
        const nodeY =
          (rect.top - canvasRect.top - this.viewport.y) / this.viewport.zoom;
        const nodeWidth = rect.width / this.viewport.zoom;
        const nodeHeight = rect.height / this.viewport.zoom;

        // Determine which edge of the node is closest to the mouse
        const relativeX = mouseX - nodeX;
        const relativeY = mouseY - nodeY;

        // Clamp to node edges for clean connection start points
        if (relativeX <= 0) {
          // Left edge
          handleX = nodeX;
          handleY = Math.max(nodeY, Math.min(nodeY + nodeHeight, mouseY));
        } else if (relativeX >= nodeWidth) {
          // Right edge
          handleX = nodeX + nodeWidth;
          handleY = Math.max(nodeY, Math.min(nodeY + nodeHeight, mouseY));
        } else if (relativeY <= 0) {
          // Top edge
          handleX = Math.max(nodeX, Math.min(nodeX + nodeWidth, mouseX));
          handleY = nodeY;
        } else if (relativeY >= nodeHeight) {
          // Bottom edge
          handleX = Math.max(nodeX, Math.min(nodeX + nodeWidth, mouseX));
          handleY = nodeY + nodeHeight;
        }
      }
    }

    this.tempConnection = {
      isActive: true,
      sourceNodeId: data.nodeId,
      sourceHandleType: data.handleType,
      sourceX: handleX,
      sourceY: handleY,
      targetX: mouseX,
      targetY: mouseY,
    };

    this.connectionStarted.emit(data);
  }

  onDeleteNode(nodeId: string): void {
    // Save history before deleting node
    this.saveHistoryBeforeAction();

    // Remove node from internal state
    this.nodes = this.nodes.filter((node) => node.id !== nodeId);

    // Remove any edges connected to this node
    this.edges = this.edges.filter(
      (edge) => edge.source !== nodeId && edge.target !== nodeId,
    );

    this.nodeRemoved.emit(nodeId);
    if (this.selectedNodeId === nodeId) {
      this.selectedNodeId = null;
    }

    this.updateNodeConnectionPoints();
  }

  onCanvasMouseMove(event: MouseEvent): void {
    // Handle canvas panning
    if (this.viewport.isDragging && this.enablePan) {
      const deltaX = event.clientX - this.viewport.lastMouseX;
      const deltaY = event.clientY - this.viewport.lastMouseY;

      this.viewport.x += deltaX;
      this.viewport.y += deltaY;

      this.viewport.lastMouseX = event.clientX;
      this.viewport.lastMouseY = event.clientY;

      this.cdr.detectChanges();
      return;
    }

    if (!this.tempConnection.isActive) return;

    // Update the temporary connection endpoint
    const canvasRect =
      this.canvasContainer.nativeElement.getBoundingClientRect();
    const targetX =
      (event.clientX - canvasRect.left - this.viewport.x) / this.viewport.zoom;
    const targetY =
      (event.clientY - canvasRect.top - this.viewport.y) / this.viewport.zoom;

    this.tempConnection = {
      ...this.tempConnection,
      targetX,
      targetY,
    };

    this.cdr.detectChanges();
  }

  onCanvasWheel(event: WheelEvent): void {
    if (!this.enableZoom || !this.mouseInteractionsEnabled) return;
    event.preventDefault();

    const delta = -event.deltaY;
    const zoomSpeed = 0.001;
    const newZoom = Math.max(
      this.minZoom,
      Math.min(this.maxZoom, this.viewport.zoom + delta * zoomSpeed),
    );

    if (newZoom !== this.viewport.zoom) {
      const rect = this.canvasContainer.nativeElement.getBoundingClientRect();
      const mouseX = event.clientX - rect.left;
      const mouseY = event.clientY - rect.top;

      const zoomRatio = newZoom / this.viewport.zoom;
      const newX = mouseX - (mouseX - this.viewport.x) * zoomRatio;
      const newY = mouseY - (mouseY - this.viewport.y) * zoomRatio;

      this.viewport.zoom = newZoom;
      this.viewport.x = newX;
      this.viewport.y = newY;

      this.updateNodeConnectionPoints();
      this.viewportChanged.emit(this.viewport);
      this.cdr.detectChanges();
    }
  }

  onCanvasMouseDown(event: MouseEvent): void {
    if (!this.enablePan || !this.mouseInteractionsEnabled) return;

    // Start canvas dragging when:
    // 1. Middle mouse button is pressed
    // 2. Alt+left click
    // 3. Pan mode is active and left click
    if (
      event.button === 1 ||
      (event.button === 0 && event.altKey) ||
      (event.button === 0 && this.canvasMode === 'pan')
    ) {
      this.viewport.isDragging = true;
      this.viewport.lastMouseX = event.clientX;
      this.viewport.lastMouseY = event.clientY;

      // Update cursor during drag
      if (this.canvasContainer) {
        this.canvasContainer.nativeElement.style.cursor = 'grabbing';
      }

      event.preventDefault();
    }
  }

  onCanvasMouseUp(event: MouseEvent): void {
    this.viewport.isDragging = false;

    // Restore cursor based on current mode
    if (this.canvasContainer) {
      this.canvasContainer.nativeElement.style.cursor =
        this.canvasMode === 'pan' ? 'grab' : 'default';
    }

    if (!this.tempConnection.isActive || !this.enableConnections) return;

    const target = event.target as HTMLElement;

    // Enhanced connection logic - allow connections to any part of a node
    const nodeElement = target.closest('[data-node-id]');

    if (nodeElement && this.tempConnection.sourceNodeId) {
      const sourceNodeId = this.tempConnection.sourceNodeId;
      const targetNodeId = nodeElement.getAttribute('data-node-id');

      // Allow connections between different nodes
      if (targetNodeId && sourceNodeId !== targetNodeId) {
        // Check if connection already exists to prevent duplicates
        const existingConnection = this.edges.find(
          (edge) =>
            (edge.source === sourceNodeId && edge.target === targetNodeId) ||
            (edge.source === targetNodeId && edge.target === sourceNodeId),
        );

        if (!existingConnection) {
          // Save history before creating connection
          this.saveHistoryBeforeAction();

          const newEdge: CanvasEdge = {
            id: `${sourceNodeId}-${targetNodeId}`,
            source: sourceNodeId,
            target: targetNodeId,
            animated: false, // Beautiful static connections
          };

          // Add edge to internal state
          this.edges.push(newEdge);

          this.connectionCreated.emit(newEdge);
        }
      }
    }

    this.tempConnection = { isActive: false };
  }

  updateNodeConnectionPoints(): void {
    if (this.updateConnectionPointsFrame) {
      cancelAnimationFrame(this.updateConnectionPointsFrame);
    }

    // SIMPLIFIED: Immediate update for consistent connection rendering
    this.updateConnectionPointsFrame = requestAnimationFrame(() => {
      this.nodeConnectionPoints = {};

      for (const node of this.nodes) {
        const nodeElement = document.querySelector(
          `[data-node-id="${node.id}"]`,
        ) as HTMLElement;

        // Calculate connection points even for nodes outside viewport
        let nodeX: number, nodeY: number, nodeWidth: number, nodeHeight: number;

        if (nodeElement) {
          // Node is in DOM - use actual measurements
          const rect = nodeElement.getBoundingClientRect();
          const canvasRect =
            this.canvasContainer?.nativeElement.getBoundingClientRect();
          if (!canvasRect) continue;

          // In execute mode, always use the actual DOM position since CSS transforms the layout
          if (this.isExecuteMode) {
            // Use actual DOM position without viewport adjustments for execute mode
            nodeX = rect.left - canvasRect.left;
            nodeY = rect.top - canvasRect.top;
            nodeWidth = rect.width;
            nodeHeight = rect.height;
          } else {
            // Build mode - use viewport-adjusted coordinates
            nodeX =
              (rect.left - canvasRect.left - this.viewport.x) /
              this.viewport.zoom;
            nodeY =
              (rect.top - canvasRect.top - this.viewport.y) /
              this.viewport.zoom;
            nodeWidth = rect.width / this.viewport.zoom;
            nodeHeight = rect.height / this.viewport.zoom;
          }
        } else {
          // Node is outside viewport - use node position data
          nodeX = node.position.x;
          nodeY = node.position.y;
          // Use default dimensions based on mode
          nodeWidth = this.isExecuteMode ? 55 : 90; // Execute mode nodes are 55px (circular with border)
          nodeHeight = this.isExecuteMode ? 55 : 48; // Execute mode nodes are 55px (circular with border)
        }

        if (this.isExecuteMode) {
          // Execute mode - icon center to icon center connections
          const centerX = nodeX + nodeWidth / 2;
          const centerY = nodeY + nodeHeight / 2;

          // All connection points are at the center of the circular icon for icon-to-icon connections
          this.nodeConnectionPoints[node.id] = {
            top: { x: centerX, y: centerY },
            right: { x: centerX, y: centerY },
            bottom: { x: centerX, y: centerY },
            left: { x: centerX, y: centerY },
          };

          console.log(
            `Execute mode node ${node.id}: center(${centerX}, ${centerY})`,
          );
        } else {
          // Build mode - account for node structure and padding
          // The node has 10px padding and the icon is 40px with center at 30px from left edge
          const iconCenterX = nodeX + 30; // 10px padding + 20px to icon center
          const centerY = nodeY + nodeHeight / 2; // Vertical center

          // Create connection points optimized for the node structure
          this.nodeConnectionPoints[node.id] = {
            top: { x: iconCenterX, y: nodeY },
            right: { x: nodeX + nodeWidth, y: centerY },
            bottom: { x: iconCenterX, y: nodeY + nodeHeight },
            left: { x: nodeX, y: centerY },
          };
        }
      }

      // Trigger change detection immediately for consistent rendering
      this.cdr.detectChanges();
      this.updateConnectionPointsFrame = null;
    });
  }

  // REMOVED getEdgePath getter to prevent infinite loops
  // The template now uses edge.pathData directly

  private calculateEdgePath(
    sourceNode: CanvasNode,
    targetNode: CanvasNode,
  ): string {
    // Calculate connection points based on mode
    let sourceX: number, sourceY: number, targetX: number, targetY: number;

    if (this.isExecuteMode) {
      // Execute mode - center to center
      sourceX = sourceNode.position.x + 27.5; // Center of 55px node
      sourceY = sourceNode.position.y + 27.5;
      targetX = targetNode.position.x + 27.5;
      targetY = targetNode.position.y + 27.5;
    } else {
      // Build mode - icon center to icon center for consistency
      sourceX = sourceNode.position.x + 30; // Icon center (10px padding + 20px to center)
      sourceY = sourceNode.position.y + 30; // Icon center
      targetX = targetNode.position.x + 30;
      targetY = targetNode.position.y + 30;
    }

    // Create simple, reliable connection path
    return this.createConnectionPath(
      { x: sourceX, y: sourceY },
      { x: targetX, y: targetY },
    );
  }

  // Pre-calculate all edge paths when nodes or edges change
  public updateEdgePaths(): void {
    console.log(
      '🔗 updateEdgePaths - Current nodes:',
      this.nodes.map((n) => ({ id: n.id, position: n.position })),
    );
    console.log(
      '🔗 updateEdgePaths - Current edges:',
      this.edges.map((e) => ({ id: e.id, source: e.source, target: e.target })),
    );

    this.edges = this.edges.map((edge) => {
      const sourceNode = this.nodes.find((n) => n.id === edge.source);
      const targetNode = this.nodes.find((n) => n.id === edge.target);

      if (sourceNode && targetNode) {
        const pathData = this.calculateEdgePath(sourceNode, targetNode);
        console.log(`🔗 SIMPLE: ${edge.id} -> ${pathData}`);
        console.log(
          `🔗 SOURCE: ${sourceNode.id} at (${sourceNode.position.x}, ${sourceNode.position.y})`,
        );
        console.log(
          `🔗 TARGET: ${targetNode.id} at (${targetNode.position.x}, ${targetNode.position.y})`,
        );
        return {
          ...edge,
          pathData: pathData,
        } as any;
      } else {
        // Fallback for missing nodes
        console.log(
          `🔗 WARNING: Missing nodes for edge ${edge.id}, using empty path`,
        );
        return {
          ...edge,
          pathData: '',
        } as any;
      }
    });

    console.log('🔗 updateEdgePaths completed - All edges now have pathData');
    this.cdr.detectChanges();
  }

  private findNearestConnectionPoints(
    sourcePoints: any,
    targetPoints: any,
  ): {
    sourcePoint: { x: number; y: number };
    targetPoint: { x: number; y: number };
  } {
    if (this.isExecuteMode) {
      // Execute mode - icon center to icon center connections
      // Since all connection points are at the center, use the center point directly
      return {
        sourcePoint: sourcePoints.top, // All points are the same (center), so any will work
        targetPoint: targetPoints.top, // All points are the same (center), so any will work
      };
    } else {
      // Build mode - find optimal connection points for any node placement
      // Calculate all possible connection combinations
      const sourcePointOptions = [
        { point: sourcePoints.right, name: 'right' },
        { point: sourcePoints.bottom, name: 'bottom' },
        { point: sourcePoints.left, name: 'left' },
        { point: sourcePoints.top, name: 'top' },
      ];

      const targetPointOptions = [
        { point: targetPoints.left, name: 'left' },
        { point: targetPoints.top, name: 'top' },
        { point: targetPoints.right, name: 'right' },
        { point: targetPoints.bottom, name: 'bottom' },
      ];

      let bestDistance = Infinity;
      let bestSourcePoint = sourcePoints.right;
      let bestTargetPoint = targetPoints.left;

      // Find the shortest distance combination
      for (const sourceOption of sourcePointOptions) {
        for (const targetOption of targetPointOptions) {
          const distance = Math.sqrt(
            Math.pow(targetOption.point.x - sourceOption.point.x, 2) +
              Math.pow(targetOption.point.y - sourceOption.point.y, 2),
          );

          if (distance < bestDistance) {
            bestDistance = distance;
            bestSourcePoint = sourceOption.point;
            bestTargetPoint = targetOption.point;
          }
        }
      }

      return {
        sourcePoint: bestSourcePoint,
        targetPoint: bestTargetPoint,
      };
    }
  }

  private getConnectionDirection(
    sourcePoint: { x: number; y: number },
    targetPoint: { x: number; y: number },
  ): string {
    const dx = targetPoint.x - sourcePoint.x;
    const dy = targetPoint.y - sourcePoint.y;

    if (Math.abs(dx) > Math.abs(dy)) {
      return dx > 0 ? 'right' : 'left';
    } else {
      return dy > 0 ? 'bottom' : 'top';
    }
  }

  private adjustTargetForArrow(
    targetPoint: { x: number; y: number },
    direction: string,
    offset: number,
  ): { x: number; y: number } {
    switch (direction) {
      case 'right':
        return { x: targetPoint.x - offset, y: targetPoint.y };
      case 'left':
        return { x: targetPoint.x + offset, y: targetPoint.y };
      case 'bottom':
        return { x: targetPoint.x, y: targetPoint.y - offset };
      case 'top':
        return { x: targetPoint.x, y: targetPoint.y + offset };
      default:
        return targetPoint;
    }
  }

  private createConnectionPath(
    sourcePoint: { x: number; y: number },
    targetPoint: { x: number; y: number },
  ): string {
    // SIMPLIFIED: Create straight lines for consistent vertical stacking
    const sourceX = sourcePoint.x;
    const sourceY = sourcePoint.y;
    const targetX = targetPoint.x;
    const targetY = targetPoint.y;

    if (this.isExecuteMode) {
      // Execute mode - direct line between centers with arrow spacing
      const dx = targetX - sourceX;
      const dy = targetY - sourceY;
      const length = Math.sqrt(dx * dx + dy * dy);

      if (length < 20) {
        return `M ${sourceX} ${sourceY} L ${targetX} ${targetY}`;
      }

      const unitX = dx / length;
      const unitY = dy / length;
      const sourceRadius = 27;
      const targetRadius = 20;
      const arrowSpace = 8;

      const adjustedSourceX = sourceX + unitX * sourceRadius;
      const adjustedSourceY = sourceY + unitY * sourceRadius;
      const adjustedTargetX = targetX - unitX * (targetRadius + arrowSpace);
      const adjustedTargetY = targetY - unitY * (targetRadius + arrowSpace);

      return `M ${adjustedSourceX} ${adjustedSourceY} L ${adjustedTargetX} ${adjustedTargetY}`;
    } else {
      // Build mode - ALWAYS STRAIGHT LINES regardless of node positioning
      const dx = targetX - sourceX;
      const dy = targetY - sourceY;
      const distance = Math.sqrt(dx * dx + dy * dy);

      if (distance < 20) {
        // Very close nodes - direct line
        return `M ${sourceX} ${sourceY} L ${targetX} ${targetY}`;
      }

      // Calculate arrow spacing to ensure arrows are visible
      const unitX = dx / distance;
      const unitY = dy / distance;

      // Adjust source to start from node edge
      const sourceRadius = 30; // Start from edge of source node
      const adjustedSourceX = sourceX + unitX * sourceRadius;
      const adjustedSourceY = sourceY + unitY * sourceRadius;

      // Adjust target to end before the node (leave space for arrow)
      const targetNodeRadius = 30; // Target node radius
      const arrowLength = 15; // Space for the arrow itself
      const adjustedTargetX =
        targetX - unitX * (targetNodeRadius + arrowLength);
      const adjustedTargetY =
        targetY - unitY * (targetNodeRadius + arrowLength);

      // ALWAYS use straight lines - no curves
      return `M ${adjustedSourceX} ${adjustedSourceY} L ${adjustedTargetX} ${adjustedTargetY}`;
    }
  }

  getTempConnectionPath(): string {
    if (
      !this.tempConnection.isActive ||
      this.tempConnection.sourceX === undefined ||
      this.tempConnection.sourceY === undefined ||
      this.tempConnection.targetX === undefined ||
      this.tempConnection.targetY === undefined
    ) {
      return '';
    }

    const sourceX = this.tempConnection.sourceX;
    const sourceY = this.tempConnection.sourceY;
    const targetX = this.tempConnection.targetX;
    const targetY = this.tempConnection.targetY;

    // Create beautiful Bézier curves exactly like workflow editor
    const dx = targetX - sourceX;
    const dy = targetY - sourceY;
    const distance = Math.sqrt(dx * dx + dy * dy);

    // Use the exact same offset calculation as your workflow editor
    const baseOffset = Math.min(100, distance * 0.5);
    const offset = Math.max(50, baseOffset);

    // Create horizontal Bézier curve with proper control points
    const controlPointX1 = sourceX + offset;
    const controlPointX2 = targetX - offset;

    return `M ${sourceX} ${sourceY} C ${controlPointX1} ${sourceY}, ${controlPointX2} ${targetY}, ${targetX} ${targetY}`;
  }

  // Toolbar actions
  onUndo(): void {
    if (this.historyIndex > 0) {
      this.historyIndex--;
      const state = this.history[this.historyIndex];
      this.restoreState(state);
    }
    this.undoAction.emit();
  }

  onRedo(): void {
    if (this.historyIndex < this.history.length - 1) {
      this.historyIndex++;
      const state = this.history[this.historyIndex];
      this.restoreState(state);
    }
    this.redoAction.emit();
  }

  onReset(): void {
    // Save current state before reset
    this.saveToHistory();

    // Clear all nodes and edges
    this.nodes = [];
    this.edges = [];
    this.selectedNodeId = null;
    this.tempConnection = { isActive: false };

    // Reset viewport
    this.resetViewport();

    // Emit state change to parent component
    this.stateChanged.emit({
      nodes: [],
      edges: [],
    });

    // Emit events
    this.nodeRemoved.emit('all'); // Special case for clearing all
    this.resetAction.emit();
  }

  onPrimaryButtonClick(): void {
    this.primaryButtonClicked.emit();
  }

  // History management methods
  private saveToHistory(): void {
    // Don't save history during state restoration
    if (this.isRestoringState) return;

    // Remove any history after current index (when we're not at the end)
    this.history = this.history.slice(0, this.historyIndex + 1);

    // Add current state to history
    const currentState = {
      nodes: JSON.parse(JSON.stringify(this.nodes)),
      edges: JSON.parse(JSON.stringify(this.edges)),
    };

    this.history.push(currentState);
    this.historyIndex = this.history.length - 1;

    // Limit history size
    if (this.history.length > this.maxHistorySize) {
      this.history.shift();
      this.historyIndex--;
    }
  }

  private restoreState(state: {
    nodes: CanvasNode[];
    edges: CanvasEdge[];
  }): void {
    this.isRestoringState = true;

    // Update the internal state
    this.nodes = [...state.nodes];
    this.edges = [...state.edges];

    // Clear selection and temp connections
    this.selectedNodeId = null;
    this.tempConnection = { isActive: false };

    // Update connection points
    this.updateNodeConnectionPoints();

    // Emit state change to parent component
    this.stateChanged.emit({
      nodes: [...this.nodes],
      edges: [...this.edges],
    });

    // Reset flag after a short delay
    setTimeout(() => {
      this.isRestoringState = false;
    }, 100);
  }

  // Save history before actions
  private saveHistoryBeforeAction(): void {
    this.saveToHistory();
  }

  // Public methods for external components to add/remove nodes and edges
  addNode(node: CanvasNode): void {
    this.saveHistoryBeforeAction();
    this.nodes.push(node);
    this.updateNodeConnectionPoints();
    this.nodeAdded.emit(node);
  }

  addEdge(edge: CanvasEdge): void {
    this.saveHistoryBeforeAction();
    this.edges.push(edge);
  }

  removeNode(nodeId: string): void {
    this.onDeleteNode(nodeId);
  }

  removeEdge(edgeId: string): void {
    this.saveHistoryBeforeAction();
    this.edges = this.edges.filter((edge) => edge.id !== edgeId);
  }

  // Canvas tool methods
  toggleGrid(): void {
    this.showGridDots = !this.showGridDots;
    this.showGrid = this.showGridDots;
  }

  setPanMode(): void {
    this.canvasMode = 'pan';
    this.mouseInteractionsEnabled = true;
    if (this.canvasContainer) {
      this.canvasContainer.nativeElement.style.cursor = 'grab';
    }
  }

  setSelectMode(): void {
    this.canvasMode = 'select';
    this.mouseInteractionsEnabled = true;
    if (this.canvasContainer) {
      this.canvasContainer.nativeElement.style.cursor = 'default';
    }
  }

  setZoomMode(): void {
    this.canvasMode = 'zoom';
    this.mouseInteractionsEnabled = true;
    if (this.canvasContainer) {
      this.canvasContainer.nativeElement.style.cursor = 'zoom-in';
    }
  }

  disableMouseInteractions(): void {
    this.canvasMode = 'disabled';
    this.mouseInteractionsEnabled = false;
    if (this.canvasContainer) {
      this.canvasContainer.nativeElement.style.cursor = 'not-allowed';
    }
  }

  zoomIn(): void {
    const newZoom = Math.min(this.maxZoom, this.viewport.zoom * 1.2);
    this.setZoom(newZoom);
  }

  zoomOut(): void {
    const newZoom = Math.max(this.minZoom, this.viewport.zoom / 1.2);
    this.setZoom(newZoom);
  }

  private setZoom(newZoom: number): void {
    if (newZoom !== this.viewport.zoom) {
      // Get canvas center for zoom
      const rect = this.canvasContainer.nativeElement.getBoundingClientRect();
      const centerX = rect.width / 2;
      const centerY = rect.height / 2;

      // Calculate new position to zoom towards center
      const zoomRatio = newZoom / this.viewport.zoom;
      const newX = centerX - (centerX - this.viewport.x) * zoomRatio;
      const newY = centerY - (centerY - this.viewport.y) * zoomRatio;

      // Update viewport
      this.viewport.zoom = newZoom;
      this.viewport.x = newX;
      this.viewport.y = newY;

      // Update connection points
      this.updateNodeConnectionPoints();

      // Emit viewport change
      this.viewportChanged.emit(this.viewport);
    }
  }

  // Built-in input field methods
  onAgentNameChange(value: string): void {
    this.agentNameControl.setValue(value);
    this.agentNameChanged.emit(value);
  }

  onAgentTypeChange(value: string | string[]): void {
    const finalValue = Array.isArray(value) ? value[0] : value;
    this.agentTypeControl.setValue(finalValue);
    this.agentTypeChanged.emit(finalValue);
  }

  // Built-in metadata dropdown methods
  toggleMetadataDropdown(): void {
    this.isMetadataDropdownOpen = !this.isMetadataDropdownOpen;
  }

  closeMetadataDropdown(): void {
    this.isMetadataDropdownOpen = false;
  }

  onDropdownSelect(
    selectedValue: string | string[],
    level: number,
    currentType: string,
    nextType: string,
  ): void {
    const value = Array.isArray(selectedValue)
      ? selectedValue[0]
      : selectedValue;
    if (!value) return;

    // Update the current dropdown value
    switch (currentType) {
      case 'org':
        this.orgControl.setValue(value);
        // Reset dependent dropdowns
        this.domainControl.setValue('');
        this.projectControl.setValue('');
        this.teamControl.setValue('');
        // Load domain options
        this.loadDomainOptions(value);
        break;
      case 'domain':
        this.domainControl.setValue(value);
        // Reset dependent dropdowns
        this.projectControl.setValue('');
        this.teamControl.setValue('');
        // Load project options
        this.loadProjectOptions(this.orgControl.value || '', value);
        break;
      case 'project':
        this.projectControl.setValue(value);
        // Reset dependent dropdown
        this.teamControl.setValue('');
        // Load team options
        this.loadTeamOptions(
          this.orgControl.value || '',
          this.domainControl.value || '',
          value,
        );
        break;
      case 'team':
        this.teamControl.setValue(value);
        break;
    }
  }

  applyMetadata(): void {
    const hasValues =
      this.orgControl.value ||
      this.domainControl.value ||
      this.projectControl.value ||
      this.teamControl.value;

    if (hasValues) {
      this.metadataStatus =
        this.inputFieldsConfig.metadata?.statusText?.saved ||
        'Metadata Information saved';
    } else {
      this.metadataStatus =
        this.inputFieldsConfig.metadata?.statusText?.notSaved ||
        'Metadata Information not saved';
    }

    // Emit metadata change event
    this.metadataChanged.emit({
      org: this.orgControl.value || '',
      domain: this.domainControl.value || '',
      project: this.projectControl.value || '',
      team: this.teamControl.value || '',
    });

    this.closeMetadataDropdown();
  }

  cancelMetadata(): void {
    this.closeMetadataDropdown();
  }

  // Load dropdown options (same API pattern as nav-item)
  private loadDomainOptions(org: string): void {
    const domainOptionsMap: { [key: string]: SelectOption[] } = {
      ascendion: [
        { value: 'engineering', label: 'Engineering' },
        { value: 'marketing', label: 'Marketing' },
        { value: 'sales', label: 'Sales' },
      ],
      company2: [
        { value: 'tech', label: 'Technology' },
        { value: 'operations', label: 'Operations' },
      ],
      company3: [
        { value: 'research', label: 'Research' },
        { value: 'development', label: 'Development' },
      ],
    };

    this.dropdownValues['domain'] = domainOptionsMap[org] || [];
  }

  private loadProjectOptions(org: string, domain: string): void {
    const projectOptions: SelectOption[] = [
      { value: 'project1', label: 'Project Alpha' },
      { value: 'project2', label: 'Project Beta' },
      { value: 'project3', label: 'Project Gamma' },
    ];

    this.dropdownValues['project'] = projectOptions;
  }

  private loadTeamOptions(org: string, domain: string, project: string): void {
    const teamOptions: SelectOption[] = [
      { value: 'team1', label: 'Team Alpha' },
      { value: 'team2', label: 'Team Beta' },
      { value: 'team3', label: 'Team Gamma' },
    ];

    this.dropdownValues['team'] = teamOptions;
  }

  // Toolbar is now fixed at bottom center - no drag methods needed

  // Toolbar is now fixed at bottom center via CSS - no positioning method needed

  // Agent details dropdown methods
  toggleAgentDetailsDropdown(): void {
    this.isAgentDetailsDropdownOpen = !this.isAgentDetailsDropdownOpen;
  }

  closeAgentDetailsDropdown(): void {
    this.isAgentDetailsDropdownOpen = false;
  }

  applyAgentDetails(): void {
    const name = this.agentDetailNameControl.value || '';
    const useCaseDetails = this.agentDetailControl.value || '';

    // Emit agent details change event
    this.agentDetailsChanged.emit({
      name: name,
      useCaseDetails: useCaseDetails,
    });

    this.closeAgentDetailsDropdown();
  }

  cancelAgentDetails(): void {
    this.closeAgentDetailsDropdown();
  }
}
