import { Component, Input } from '@angular/core';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'app-card',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './card.component.html',
  styleUrl: './card.component.scss'
})
export class CardComponent {
  @Input() customClass: string = '';
  @Input() isClickable: boolean = false;
  @Input() fixedHeight: boolean = false;
  @Input() noPadding: boolean = false;
  @Input() noHoverEffect: boolean = false;
  @Input() backgroundColor: string = '';
  @Input() height: string = '';
} 