.select-container {
  position: relative;
  width: 100%;
  font-family: sans-serif;
  margin-bottom: 10px;
}

.select-label {
  display: block;
  margin-bottom: 8px;
  font-size: 14px;
  font-weight: 500;
  color: var(--dropdown-text);
}

.select-field {
  width: 250px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 10px 14px;
  border: 1px solid var(--dropdown-border);
  border-radius: 8px;
  background-color: var(--dropdown-bg);
  cursor: pointer;
  transition: all 0.2s ease;
  min-height: 42px;
  
  &:hover {
    border-color: var(--dropdown-focus-border);
  }
  
  &.open {
    border-color: var(--dropdown-focus-border);
    box-shadow: 0 0 0 2px var(--dropdown-focus-shadow);
  }
  
  &.required {
    border-color: var(--dropdown-required-border);
  }
  
  &.multi-select {
    .selected-value {
      max-width: calc(100% - 20px);
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
}

.selected-value {
  font-size: 14px;
  color: var(--dropdown-text);
  
  .placeholder {
    color: var(--dropdown-placeholder);
    font-style: italic;
  }
}

.select-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  
  svg {
    transition: transform 0.2s ease;
    
    &.open {
      transform: rotate(180deg);
    }
  }
}

.dropdown-menu {
  position: absolute;
  top: 100%;
  left: 0; 
  width: 250px; /* Make dropdown wider */
  margin-top: 4px;
  background-color: var(--dropdown-bg);
  border: 1px solid var(--dropdown-border);
  border-radius: 8px;
  box-shadow: 0 4px 10px var(--dropdown-shadow);
  z-index: 100;
  max-height: 300px; /* Increased max height */
  overflow: hidden; /* Remove overflow from main container */
}

.dropdown-item {
  padding: 10px 14px;
  font-size: 14px;
  color: var(--dropdown-text);
  cursor: pointer;
  transition: background-color 0.2s ease;
  
  &:hover {
    background-color: var(--dropdown-hover-bg);
  }
  
  &.selected {
    background-color: var(--dropdown-selected-bg);
    color: var(--dropdown-selected-text);
    font-weight: 500;
  }
  
  &.active {
    background-color: var(--dropdown-hover-bg);
    color: var(--dropdown-text);
    font-weight: 500;
  }
  
  &.selected.active{
    background-color: var(--dropdown-selected-bg) !important;
    color: var(--dropdown-selected-text) !important;
  }
}

.disabled {
  opacity: 0.6;
  cursor: not-allowed;
  
  .select-field {
    cursor: not-allowed;
    background-color: var(--dropdown-disabled-bg);
    
    &:hover {
      border-color: var(--dropdown-border);
    }
  }
}

/* Multi-select specific styles */
.option-content {
  display: flex;
  align-items: center;
  width: 100%;
}

.checkbox-container {
  margin-right: 10px;
  display: flex;
  align-items: center;
}

.checkbox {
  width: 16px;
  height: 16px;
  border: 1px solid var(--dropdown-checkbox-border);
  border-radius: 3px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  
  &.checked {
    background-color: var(--dropdown-checkbox-bg);
    border-color: var(--dropdown-checkbox-bg);
  }
}

/* Search container styles */
.search-container {
  padding: 8px 12px;
  border-bottom: 1px solid var(--dropdown-border);
  background-color: var(--dropdown-bg);
  border-radius: 8px 8px 0 0;

  app-search-bar {
    width: 100%;

    ::ng-deep .search-container {
      height: 36px;
      margin: 0;
      padding: 0;
      background: transparent;
      border: none;
      backdrop-filter: none;
      box-shadow: none;
    }

    ::ng-deep .search-input {
      height: 36px;
      font-size: 14px;
      padding: 8px 12px 8px 36px;
    }
  }
}

/* Options container with scroll */
.options-container {
  max-height: 240px;
  overflow-y: auto;
  overflow-x: hidden;
}

/* No results message */
.no-results {
  padding: 16px 14px;
  text-align: center;
  color: var(--dropdown-placeholder);
  font-style: italic;
  font-size: 14px;
}