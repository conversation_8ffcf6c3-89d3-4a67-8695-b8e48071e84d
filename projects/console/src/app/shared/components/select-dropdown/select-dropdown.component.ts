import { Component, Input, Output, EventEmitter, forwardRef, ElementRef, HostListener, OnChanges, SimpleChanges } from '@angular/core';
import { CommonModule } from '@angular/common';
import { NG_VALUE_ACCESSOR, ControlValueAccessor } from '@angular/forms';
import { FormControl, FormsModule } from '@angular/forms';
import SearchBar from '../search-bar/search-bar.component';

export interface SelectOption {
  value: string;
  label: string;
}

@Component({
  selector: 'app-select-dropdown',
  standalone: true,
  imports: [CommonModule, FormsModule, SearchBar],
  templateUrl: './select-dropdown.component.html',
  styleUrls: ['./select-dropdown.component.scss'],
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => SelectDropdownComponent),
      multi: true
    }
  ]
})
export class SelectDropdownComponent implements ControlValueAccessor, OnChanges {
  @Input() options: SelectOption[] = [];
  @Input() placeholder: string = 'Select an option';
  @Input() label: string = '';
  @Input() disabled: boolean = false;
  @Input() required: boolean = false;
  @Input() isMultiSelect: boolean = false;
  @Input() dropdownWidth: string = '';
  @Input() dropdownHeight: string = '';
  @Input() value: string = '';
  @Input() control?: FormControl;
  @Input() enableSearch: boolean = false;
  @Output() selectionChange = new EventEmitter<string | string[]>();

  isOpen: boolean = false;
  selectedOption: SelectOption | null = null;
  selectedOptions: SelectOption[] = [];
  activeIndex: number = -1;
  searchQuery: string = '';
  filteredOptions: SelectOption[] = [];

  private onChange: any = () => {};
  private onTouched: any = () => {};

  constructor(private elementRef: ElementRef) {
    this.filteredOptions = this.options;
  }

  writeValue(value: string | string[] | null): void {
    if (!value || (Array.isArray(value) && value.length === 0)) {
      this.selectedOption = null;
      this.selectedOptions = [];
      return;
    }
    if (this.isMultiSelect && Array.isArray(value)) {
      this.selectedOptions = this.options.filter(option => value.includes(option.value));
      this.selectedOption = null;
    } else if (!this.isMultiSelect && typeof value === 'string') {
      this.selectedOption = this.options.find(option => option.value === value) || null;
      this.selectedOptions = [];
    }
  }

  registerOnChange(fn: any): void { this.onChange = fn; }
  registerOnTouched(fn: any): void { this.onTouched = fn; }
  setDisabledState(isDisabled: boolean): void { this.disabled = isDisabled; }

  static closeAllDropdownsExcept(current: SelectDropdownComponent) {
    const event = new CustomEvent('closeAllDropdowns', { detail: current });
    window.dispatchEvent(event);
  }

  ngOnInit() {
    window.addEventListener('closeAllDropdowns', this.handleCloseAllDropdowns);
    this.filteredOptions = this.options;
  }

  ngOnDestroy() {
    window.removeEventListener('closeAllDropdowns', this.handleCloseAllDropdowns);
  }

  handleCloseAllDropdowns = (event: any) => {
    if (event.detail !== this) {
      this.closeDropdown();
    }
  };

  toggleDropdown(): void {
    if (!this.disabled) {
      if (!this.isOpen) {
        SelectDropdownComponent.closeAllDropdownsExcept(this);
      }
      this.isOpen = !this.isOpen;
      if (this.isOpen) {
        this.onTouched();
        this.activeIndex = this.getInitialActiveIndex();
      }
    }
  }

  selectOption(option: SelectOption, event?: Event): void {
    if (this.isMultiSelect) {
      event?.stopPropagation();
      const isSelected = this.selectedOptions.some(item => item.value === option.value);
      if (isSelected) {
        this.selectedOptions = this.selectedOptions.filter(item => item.value !== option.value);
      } else {
        this.selectedOptions = [...this.selectedOptions, option];
      }
      const selectedValues = this.selectedOptions.map(opt => opt.value);
      this.onChange(selectedValues);
      this.selectionChange.emit(selectedValues);
    } else {
      if (!option || option.value === undefined) return;
      const currentValue = this.selectedOption ? this.selectedOption.value : undefined;
      if (currentValue !== option.value) {
        this.selectedOption = option;
        if (this.control && typeof this.control.setValue === 'function') {
          this.control.setValue(option.value);
        } else {
          this.value = option.value;
        }
        this.onChange(option.value);
        this.selectionChange.emit(option.value);
      }
      this.isOpen = false;
    }
  }

  isOptionSelected(option: SelectOption): boolean {
    if (this.isMultiSelect) {
      return this.selectedOptions.some(item => item.value === option.value);
    } else {
      return this.selectedOption?.value === option.value;
    }
  }

  getSelectedLabel(): string {
    if (this.isMultiSelect) {
      if (this.selectedOptions.length === 0) {
        return this.placeholder;
      } else if (this.selectedOptions.length === 1) {
        return this.selectedOptions[0].label;
      } else {
        return `${this.selectedOptions.length} items selected`;
      }
    } else {
      return this.selectedOption ? this.selectedOption.label : this.placeholder;
    }
  }

  closeDropdown(): void {
    this.isOpen = false;
  }

  getInitialActiveIndex(): number {
    if (this.isMultiSelect) {
      return this.options.length > 0 ? 0 : -1;
    } else if (this.selectedOption) {
      return this.options.findIndex(opt => opt.value === this.selectedOption?.value);
    } else {
      return this.options.length > 0 ? 0 : -1;
    }
  }

  @HostListener('document:click', ['$event'])
  onDocumentClick(event: MouseEvent) {
    if (this.isOpen && !this.elementRef.nativeElement.contains(event.target)) {
      this.closeDropdown();
    }
  }

  @HostListener('keydown', ['$event'])
  onKeydown(event: KeyboardEvent) {
    const isFieldFocused = document.activeElement === this.elementRef.nativeElement.querySelector('.select-field');
    if (!this.isOpen && isFieldFocused && (event.key === 'Enter' || event.key === ' ')) {
      event.preventDefault();
      SelectDropdownComponent.closeAllDropdownsExcept(this);
      this.toggleDropdown();
      return;
    }
    if (!this.isOpen) return;
    if (event.key === 'ArrowDown') {
      event.preventDefault();
      this.activeIndex = (this.activeIndex + 1) % this.options.length;
      this.scrollToActive();
    } else if (event.key === 'ArrowUp') {
      event.preventDefault();
      this.activeIndex = (this.activeIndex - 1 + this.options.length) % this.options.length;
      this.scrollToActive();
    } else if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      if (this.activeIndex >= 0 && this.activeIndex < this.options.length) {
        this.selectOption(this.options[this.activeIndex]);
      }
    } else if (event.key === 'Escape') {
      this.closeDropdown();
    }
  }

  scrollToActive() {
    setTimeout(() => {
      const menu = this.elementRef.nativeElement.querySelector('.dropdown-menu');
      const active = menu?.querySelector('.dropdown-item.active');
      if (active && menu) {
        active.scrollIntoView({ block: 'nearest' });
      }
    });
  }

  ngOnChanges(changes: SimpleChanges) {
    if (this.control && !this.isMultiSelect) {
      const controlValue = this.control.value ?? '';
      this.selectedOption = (this.options || []).find(option => option.value == controlValue) || null;
    } else if (changes['value'] && !this.isMultiSelect) {
      this.selectedOption = (this.options || []).find(option => option.value == this.value) || null;
    }

    if (changes['options']) {
      this.filteredOptions = this.options;
      this.filterOptions();
    }
  }

  // Search functionality methods
  onSearchChange(query: string): void {
    this.searchQuery = query;
    this.filterOptions();
  }

  private filterOptions(): void {
    if (!this.searchQuery || this.searchQuery.trim() === '') {
      this.filteredOptions = this.options;
    } else {
      const query = this.searchQuery.toLowerCase().trim();
      this.filteredOptions = this.options.filter(option =>
        option.label.toLowerCase().includes(query) ||
        option.value.toLowerCase().includes(query)
      );
    }
  }
} 