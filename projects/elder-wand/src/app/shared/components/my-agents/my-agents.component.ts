import { CommonModule } from '@angular/common';
import { Component } from '@angular/core';
import { Router } from '@angular/router';
import { IconComponent } from "@ava/play-comp-library";

interface AgentCardButton {
  label: string;
  iconName?: string;
  type?: 'primary' | 'secondary';
}

interface AgentCard {
  iconName: string;
  iconColor: string;
  title: string;
  description: string;
  buttons: AgentCardButton[];
}

@Component({
  selector: 'app-my-agents',
  templateUrl: './my-agents.component.html',
  styleUrls: ['./my-agents.component.scss'],
  imports: [IconComponent, CommonModule]
})
export class MyAgentsComponent {
  constructor(private router: Router) {}

  cards: AgentCard[] = [
    {
      iconName: 'bot',
      iconColor: 'black',
      title: 'Create Agent',
      description: 'Create a smart AI agent to streamline tasks, automate workflows, and boost efficiency effortlessly.',
      buttons: [
        { label: 'Create', iconName: 'plus', type: 'primary' }
      ]
    },
    {
      iconName: 'workflow',
      iconColor: 'black',
      title: 'Create Agentic Workflows',
      description: 'Build and manage advanced, multi-step automation flows to handle complex tasks with ease.',
      buttons: [
        { label: 'Create', iconName: 'plus', type: 'primary' }
      ]
    },
    {
      iconName: 'file-text',
      iconColor: 'black',
      title: 'Read & Update My List',
      description: 'Configure AI to read, update, and manage data across your systems seamlessly.',
      buttons: [
        { label: 'Read', iconName: 'book-open', type: 'secondary' },
        { label: 'Update', iconName: 'cloud-upload', type: 'primary' }
      ]
    },
    {
      iconName: 'wrench',
      iconColor: 'black',
      title: 'Create Tool',
      description: 'Configure AI to read, update, and manage data across your systems seamlessly.',
      buttons: [
        { label: 'Create', iconName: 'plus', type: 'primary' }
      ]
    },
    {
      iconName: 'shield-check',
      iconColor: 'black',
      title: 'Create Guardrail',
      description: 'Configure AI to read, update, and manage data across your systems seamlessly.',
      buttons: [
        { label: 'Create', iconName: 'plus', type: 'primary' }
      ]
    },
    {
      iconName: 'book-text',
      iconColor: 'black',
      title: 'Create Knowledge Base',
      description: 'Configure AI to read, update, and manage data across your systems seamlessly.',
      buttons: [
        { label: 'Create', iconName: 'plus', type: 'primary' }
      ]
    }
  ];

  onButtonClick(card: AgentCard, button: AgentCardButton): void {
    console.log('Button clicked:', {
      cardTitle: card.title,
      buttonLabel: button.label,
      buttonIcon: button.iconName,
      cardIcon: card.iconName
    });

    // Handle navigation for specific button combinations
    if (card.title === 'Read & Update My List' && button.label === 'Read') {
      // Navigate to my-agent-home page
      this.router.navigate(['/my-agent-home']);
      return;
    }

    // Show alert for other buttons (future functionality)
    const alertMessage = this.getAlertMessage(card.title, button.label);
    alert(alertMessage);
  }

  private getAlertMessage(cardTitle: string, buttonLabel: string): string {
    const actionKey = `${cardTitle}-${buttonLabel}`;

    const alertMessages: { [key: string]: string } = {
      'Create Agent-Create': '🤖 Navigating to Create Agent page...\n\nHere you will be able to create a new AI agent with custom configurations.',
      'Create Agentic Workflows-Create': '⚡ Navigating to Create Agentic Workflows page...\n\nHere you will be able to build multi-step automation flows.',
      'Read & Update My List-Read': '📖 Navigating to Read My List page...\n\nHere you will be able to view and read your data lists.',
      'Read & Update My List-Update': '📝 Navigating to Update My List page...\n\nHere you will be able to modify and update your data lists.',
      'Create Tool-Create': '🔧 Navigating to Create Tool page...\n\nHere you will be able to create custom tools for your agents.',
      'Create Guardrail-Create': '🛡️ Navigating to Create Guardrail page...\n\nHere you will be able to set up safety and compliance rules.',
      'Create Knowledge Base-Create': '📚 Navigating to Create Knowledge Base page...\n\nHere you will be able to build and manage your knowledge repository.'
    };

    return alertMessages[actionKey] || `🚀 ${buttonLabel} action for ${cardTitle}\n\nThis functionality will be implemented soon!`;
  }
}
