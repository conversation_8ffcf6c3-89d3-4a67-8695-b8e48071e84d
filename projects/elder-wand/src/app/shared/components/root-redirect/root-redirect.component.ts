import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { TokenStorageService } from '@shared/auth/services/token-storage.service';
import { AuthService } from '@shared/auth/services/auth.service';
import { catchError, of } from 'rxjs';

@Component({
  selector: 'app-root-redirect',
  standalone: true,
  template: `
    <div
      style="display: flex; justify-content: center; align-items: center; height: 100vh; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white;"
    >
      <div style="text-align: center;">
        <div style="font-size: 2rem; margin-bottom: 1rem;">🌟 Elder Wand</div>
        <div style="font-size: 1rem; opacity: 0.8;">{{ redirectMessage }}</div>
      </div>
    </div>
  `,
})
export class RootRedirectComponent implements OnInit {
  redirectMessage = 'Checking authentication...';

  constructor(
    private router: Router,
    private tokenStorage: TokenStorageService,
    private authService: AuthService,
  ) {}

  ngOnInit(): void {
    console.log('🔀 RootRedirectComponent: Determining redirect destination');
    this.determineRedirect();
  }

  private determineRedirect(): void {
    const accessToken = this.tokenStorage.getAccessToken();
    const refreshToken = this.tokenStorage.getRefreshToken();

    // If we have an access token, go to dashboard
    if (accessToken) {
      console.log('✅ User authenticated, redirecting to dashboard');
      this.redirectMessage = 'Welcome back! Redirecting to your dashboard...';
      setTimeout(() => {
        this.router.navigate(['/dashboard']);
      }, 1000);
      return;
    }

    // If we have a refresh token but no access token, try to refresh
    if (!accessToken && refreshToken) {
      console.log('🔄 Attempting token refresh');
      this.redirectMessage = 'Refreshing your session...';

      this.authService
        .refreshToken(refreshToken)
        .pipe(
          catchError((error) => {
            console.log('❌ Token refresh failed, redirecting to marketplace');
            this.redirectMessage =
              'Session expired. Redirecting to marketplace...';
            setTimeout(() => {
              this.router.navigate(['/marketplace']);
            }, 1500);
            return of(null);
          }),
        )
        .subscribe((success) => {
          if (success) {
            console.log(
              '✅ Token refreshed successfully, redirecting to dashboard',
            );
            this.redirectMessage =
              'Session refreshed! Redirecting to your dashboard...';
            setTimeout(() => {
              this.router.navigate(['/dashboard']);
            }, 1000);
          }
        });
      return;
    }

    // If no tokens at all, redirect to marketplace
    console.log('🏪 No authentication found, redirecting to marketplace');
    this.redirectMessage = 'Welcome! Redirecting to marketplace...';
    setTimeout(() => {
      this.router.navigate(['/marketplace']);
    }, 1000);
  }
}
