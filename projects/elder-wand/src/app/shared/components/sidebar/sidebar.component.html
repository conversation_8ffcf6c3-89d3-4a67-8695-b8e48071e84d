<div class="sidebar-container">
  <!-- Top section with Aava logo -->
  <div class="sidebar-top">
    <div class="logo-container" (click)="onLogoClick()">
      <img src="assets/icons/Aava.svg" alt="Aava Logo" class="aava-logo" />
    </div>

    <!-- Navigation icons right after logo -->
    <div class="sidebar-nav">
      <div
        *ngFor="let item of sidebarItems"
        class="nav-item"
        [class.active]="item.isActive"
        (click)="onItemClick(item)"
        [attr.aria-label]="item.label"
      >
        <ava-icon
          [iconName]="item.iconName"
          [iconSize]="20"
          [iconColor]="'#666D99'"
          [cursor]="true"
          (userClick)="onItemClick(item)"
        ></ava-icon>
      </div>
    </div>
  </div>

  <!-- Divider line -->
  <div class="sidebar-divider"></div>

  <!-- Bottom section with account icon -->
  <div class="sidebar-bottom">
    <div class="account-icon" (click)="onAccountClick()">
      <ava-icon
        iconName="user"
        [iconSize]="20"
        iconColor="#FFFFFF"
        [cursor]="true"
        (userClick)="onAccountClick()"
      ></ava-icon>
    </div>
  </div>
</div>
