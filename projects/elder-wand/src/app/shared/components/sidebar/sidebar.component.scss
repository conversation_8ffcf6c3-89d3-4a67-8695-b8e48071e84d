.sidebar-container {
  // Exact layout from requirements
  display: flex;
  width: 80px;
  height: 947px;
  padding: 32px 10px 32px 10px; // More padding at bottom
  flex-direction: column;
  justify-content: flex-start; // Changed to flex-start for manual spacing
  align-items: center;
  flex-shrink: 0;

  // Exact styling from requirements
  border-radius: 24px;
  border: 2px solid #FFF;
  background: linear-gradient(0deg, rgba(255, 255, 255, 0.30) 0%, rgba(255, 255, 255, 0.30) 100%),
    linear-gradient(114deg, rgba(240, 235, 248, 0.50) 1.5%,
      rgba(255, 255, 255, 0.50) 45.85%,
      rgba(245, 233, 247, 0.50) 98.86%);
  box-shadow: 0px 2px 2px -3px #F0F1F2,
    0px 0px 6px -2px #D1D3D8;
  backdrop-filter: blur(48px);
}

.sidebar-top {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 48px; // Gap between logo and navigation icons
}

.logo-container {
  // First image from assets with 48px dimensions as per requirements
  width: 48px;
  height: 48px;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 12px;
  overflow: hidden;
  cursor: pointer;
}

.aava-logo {
  width: 48px;
  height: 48px;
  object-fit: contain;
}

.sidebar-nav {
  display: flex;
  flex-direction: column;
  gap: 24px;
  align-items: center;
}

.nav-item {
  width: 40px;
  height: 40px;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 12px;
  cursor: pointer;
  position: relative;

  // Simple hover effect - light purple background
  &:hover {
    background-color: rgba(167, 123, 243, 0.1);
  }

  // Active state with purple background
  &.active {
    background-color: rgba(167, 123, 243, 0.2);
  }

  // Icon size 20px as per requirements
  ava-icon {
    display: flex;
    justify-content: center;
    align-items: center;

    ::ng-deep .ava-icon-container {
      background: transparent;
      border: none;
      padding: 0;
      width: 20px;
      height: 20px;

      lucide-icon {
        width: 20px !important;
        height: 20px !important;
      }
    }
  }
}

// Divider between navigation and account icon
.sidebar-divider {
  width: 40px;
  height: 1px;
  background-color: rgba(167, 123, 243, 0.2);
  margin: auto 0; // This will push the account icon to the bottom
}

.sidebar-bottom {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: auto; // Push to bottom
}

.account-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  background-color: #A77BF3; // Purple background for account icon

  // Black background on hover for account icon
  &:hover {
    background-color: #31195a;
  }

  ava-icon {
    display: flex;
    justify-content: center;
    align-items: center;

    ::ng-deep .ava-icon-container {
      background: transparent;
      border: none;
      padding: 0;
      width: 20px;
      height: 20px;

      lucide-icon {
        width: 20px !important;
        height: 20px !important;
      }
    }
  }
}



// Responsive adjustments
@media (max-width: 768px) {
  .sidebar-container {
    width: 60px;
    height: auto;
    min-height: 400px;
    padding: 20px 8px;
    gap: 32px;
    left: 10px;
  }

  .logo-container {
    width: 36px;
    height: 36px;
  }

  .aava-logo {
    width: 36px;
    height: 36px;
  }

  .nav-item {
    width: 36px;
    height: 36px;

    ava-icon ::ng-deep .ava-icon-container lucide-icon {
      width: 18px !important;
      height: 18px !important;
    }
  }

  .account-icon {
    width: 36px;
    height: 36px;
  }
}

@media (max-width: 480px) {
  .sidebar-container {
    width: 50px;
    padding: 16px 6px;
    gap: 24px;
    left: 5px;
  }

  .logo-container {
    width: 32px;
    height: 32px;
  }

  .aava-logo {
    width: 32px;
    height: 32px;
  }

  .nav-item {
    width: 32px;
    height: 32px;

    ava-icon ::ng-deep .ava-icon-container lucide-icon {
      width: 16px !important;
      height: 16px !important;
    }
  }

  .account-icon {
    width: 32px;
    height: 32px;
  }
}